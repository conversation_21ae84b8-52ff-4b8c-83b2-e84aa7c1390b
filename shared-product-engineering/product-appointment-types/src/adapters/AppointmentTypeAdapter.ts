/**
 * 🔄 Universal Appointment Type Adapter
 * 
 * This adapter handles conversions between different appointment models:
 * - Domain Appointment (with timeSlot)
 * - UnifiedAppointment (with startTime/endTime)
 * - Database models
 * - Service-specific models
 */

import type { Appointment, TimeSlot } from '../appointment/types';
import type { UnifiedAppointment, UnifiedAppointmentStatus } from '../appointment/unified-types';

// ============================================================================
// Type Guards
// ============================================================================

export function isUnifiedAppointment(obj: any): obj is UnifiedAppointment {
  return obj && 
    typeof obj.id === 'string' &&
    typeof obj.startTime !== 'undefined' &&
    typeof obj.endTime !== 'undefined' &&
    typeof obj.customerName === 'string' &&
    typeof obj.customerEmail === 'string';
}

export function isDomainAppointment(obj: any): obj is Appointment {
  return obj && 
    typeof obj.id === 'string' &&
    obj.timeSlot &&
    typeof obj.timeSlot.start !== 'undefined' &&
    typeof obj.timeSlot.end !== 'undefined';
}

// ============================================================================
// Status Converters
// ============================================================================

export function domainStatusToUnified(status: Appointment['status']): UnifiedAppointmentStatus {
  switch (status) {
    case 'PENDING':
      return UnifiedAppointmentStatus.PENDING;
    case 'CONFIRMED':
      return UnifiedAppointmentStatus.CONFIRMED;
    case 'IN_PROGRESS':
      return UnifiedAppointmentStatus.IN_PROGRESS;
    case 'COMPLETED':
      return UnifiedAppointmentStatus.COMPLETED;
    case 'CANCELLED':
      return UnifiedAppointmentStatus.CANCELLED;
    case 'NO_SHOW':
      return UnifiedAppointmentStatus.NO_SHOW;
    default:
      return UnifiedAppointmentStatus.PENDING;
  }
}

export function unifiedStatusToDomain(status: UnifiedAppointmentStatus): Appointment['status'] {
  switch (status) {
    case UnifiedAppointmentStatus.PENDING:
      return 'PENDING';
    case UnifiedAppointmentStatus.CONFIRMED:
      return 'CONFIRMED';
    case UnifiedAppointmentStatus.SCHEDULED:
      return 'CONFIRMED'; // Map SCHEDULED to CONFIRMED for domain
    case UnifiedAppointmentStatus.IN_PROGRESS:
      return 'IN_PROGRESS';
    case UnifiedAppointmentStatus.COMPLETED:
      return 'COMPLETED';
    case UnifiedAppointmentStatus.CANCELLED:
      return 'CANCELLED';
    case UnifiedAppointmentStatus.NO_SHOW:
      return 'NO_SHOW';
    case UnifiedAppointmentStatus.RESCHEDULED:
      return 'PENDING'; // Map RESCHEDULED to PENDING for domain
    default:
      return 'PENDING';
  }
}

// ============================================================================
// Core Converters
// ============================================================================

/**
 * Convert Domain Appointment (with timeSlot) to UnifiedAppointment (with startTime/endTime)
 */
export function domainToUnified(
  appointment: Appointment,
  additionalData?: {
    customerName?: string;
    customerEmail?: string;
    treatmentName?: string;
    treatmentDuration?: number;
    treatmentPrice?: number;
    salonName?: string;
    staffName?: string;
    source?: 'PLANNER' | 'MANAGEMENT';
  }
): UnifiedAppointment {
  return {
    id: appointment.id,
    externalId: appointment.metadata?.externalId as string,
    
    // Customer data (required in UnifiedAppointment)
    customerId: appointment.customerId,
    customerName: additionalData?.customerName || `Customer ${appointment.customerId}`,
    customerEmail: additionalData?.customerEmail || `customer-${appointment.customerId}@example.com`,
    customerPhone: additionalData?.customerName, // Assuming phone might be in name field
    
    // Salon/Staff data
    salonId: appointment.salonId,
    salonName: additionalData?.salonName,
    staffId: appointment.staffId,
    staffName: additionalData?.staffName,
    
    // Treatment data (required in UnifiedAppointment)
    treatmentId: appointment.treatmentId,
    treatmentName: additionalData?.treatmentName || `Treatment ${appointment.treatmentId}`,
    treatmentDuration: additionalData?.treatmentDuration || 60,
    treatmentPrice: additionalData?.treatmentPrice || 0,
    
    // Time conversion: timeSlot -> startTime/endTime
    startTime: appointment.timeSlot.start,
    endTime: appointment.timeSlot.end,
    
    // Status conversion
    status: domainStatusToUnified(appointment.status),
    source: additionalData?.source || 'MANAGEMENT',
    
    // Metadata
    notes: appointment.notes,
    createdAt: appointment.createdAt,
    updatedAt: appointment.updatedAt,
  };
}

/**
 * Convert UnifiedAppointment (with startTime/endTime) to Domain Appointment (with timeSlot)
 */
export function unifiedToDomain(appointment: UnifiedAppointment): Appointment {
  return {
    id: appointment.id,
    salonId: appointment.salonId,
    customerId: appointment.customerId,
    treatmentId: appointment.treatmentId,
    staffId: appointment.staffId,
    
    // Time conversion: startTime/endTime -> timeSlot
    timeSlot: {
      start: appointment.startTime,
      end: appointment.endTime,
    },
    
    // Status conversion
    status: unifiedStatusToDomain(appointment.status),
    
    // Optional fields
    priority: 'medium', // Default priority since UnifiedAppointment doesn't have this
    notes: appointment.notes,
    cancellationReason: undefined, // Not available in UnifiedAppointment
    metadata: {
      externalId: appointment.externalId,
      customerName: appointment.customerName,
      customerEmail: appointment.customerEmail,
      treatmentName: appointment.treatmentName,
      treatmentDuration: appointment.treatmentDuration,
      treatmentPrice: appointment.treatmentPrice,
      source: appointment.source,
    },
    
    createdAt: appointment.createdAt,
    updatedAt: appointment.updatedAt,
  };
}

// ============================================================================
// Batch Converters
// ============================================================================

export function domainArrayToUnified(
  appointments: Appointment[],
  additionalDataMap?: Map<string, any>
): UnifiedAppointment[] {
  return appointments.map(appointment => 
    domainToUnified(appointment, additionalDataMap?.get(appointment.id))
  );
}

export function unifiedArrayToDomain(appointments: UnifiedAppointment[]): Appointment[] {
  return appointments.map(unifiedToDomain);
}

// ============================================================================
// Partial Converters (for updates)
// ============================================================================

export function partialDomainToUnified(
  changes: Partial<Appointment>
): Partial<UnifiedAppointment> {
  const unified: Partial<UnifiedAppointment> = {};
  
  if (changes.timeSlot) {
    unified.startTime = changes.timeSlot.start;
    unified.endTime = changes.timeSlot.end;
  }
  
  if (changes.status) {
    unified.status = domainStatusToUnified(changes.status);
  }
  
  if (changes.notes !== undefined) {
    unified.notes = changes.notes;
  }
  
  if (changes.updatedAt) {
    unified.updatedAt = changes.updatedAt;
  }
  
  return unified;
}

export function partialUnifiedToDomain(
  changes: Partial<UnifiedAppointment>
): Partial<Appointment> {
  const domain: Partial<Appointment> = {};
  
  if (changes.startTime && changes.endTime) {
    domain.timeSlot = {
      start: changes.startTime,
      end: changes.endTime,
    };
  }
  
  if (changes.status) {
    domain.status = unifiedStatusToDomain(changes.status);
  }
  
  if (changes.notes !== undefined) {
    domain.notes = changes.notes;
  }
  
  if (changes.updatedAt) {
    domain.updatedAt = changes.updatedAt;
  }
  
  return domain;
}

// ============================================================================
// Utility Functions
// ============================================================================

/**
 * Create a TimeSlot from start and end times
 */
export function createTimeSlot(startTime: Date, endTime: Date): TimeSlot {
  return { start: startTime, end: endTime };
}

/**
 * Extract start and end times from a TimeSlot
 */
export function extractTimes(timeSlot: TimeSlot): { startTime: Date; endTime: Date } {
  return { startTime: timeSlot.start, endTime: timeSlot.end };
}

/**
 * Validate that an appointment has all required fields for conversion
 */
export function validateForConversion(appointment: any): string[] {
  const errors: string[] = [];
  
  if (!appointment.id) errors.push('Missing id');
  if (!appointment.salonId) errors.push('Missing salonId');
  if (!appointment.customerId) errors.push('Missing customerId');
  if (!appointment.treatmentId) errors.push('Missing treatmentId');
  if (!appointment.staffId) errors.push('Missing staffId');
  
  return errors;
}
