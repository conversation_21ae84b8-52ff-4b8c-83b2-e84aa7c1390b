// ✨ DEPRECATED: Use @beauty-crm/platform-appointment-schema instead
// This package is being phased out in favor of the Single Source of Truth schema library

// Export events first to resolve AppointmentEvent conflict
export * from './appointment/events';
// Export types but exclude the conflicting AppointmentEvent
export type {
  Appointment,
  AppointmentCancellationRequest,
  AppointmentFeedback,
  AppointmentPriority,
  AppointmentReminder,
  AppointmentRequest,
  AppointmentRescheduleRequest,
  AppointmentSettings,
  AppointmentStatus,
  TimeSlot,
} from './appointment/types';
export * from './appointment/unified-types';

// ❌ REMOVED: Duplicate mappers and adapters - use @beauty-crm/platform-appointment-schema instead
// export * from './mappers';
// export * from './adapters/AppointmentTypeAdapter';
