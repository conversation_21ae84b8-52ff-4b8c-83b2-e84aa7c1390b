import type { UnifiedAppointment } from './index';
import { UnifiedAppointmentStatus } from './index';

export enum PrismaAppointmentStatus {
  PENDING = 'PENDING',
  CONFIRMED = 'CONFIRMED',
  CANCELLED = 'CANCELLED',
  COMPLETED = 'COMPLETED',
  RESCHEDULED = 'RESCHEDULED',
}

// This is a generic representation of the Prisma.AppointmentCreateInput
// It is up to the service to ensure that the object it creates
// conforms to the actual Prisma.AppointmentCreateInput
export interface PrismaAppointmentCreateInput {
  id: string;
  customerId: string;
  customerEmail?: string | null;
  customerName?: string | null;
  customerPhone?: string | null;
  endTime: Date;
  notes?: string | null;
  salonId: string;
  staffId?: string | null;
  startTime: Date;
  status: PrismaAppointmentStatus;
  treatmentId: string;
  createdAt: Date;
  updatedAt: Date;
}

// Status mapping functions
export const toPrismaStatus = (
  status: UnifiedAppointmentStatus
): PrismaAppointmentStatus => {
  switch (status) {
    case UnifiedAppointmentStatus.PENDING:
      return PrismaAppointmentStatus.PENDING;
    case UnifiedAppointmentStatus.CONFIRMED:
      return PrismaAppointmentStatus.CONFIRMED;
    case UnifiedAppointmentStatus.CANCELLED:
      return PrismaAppointmentStatus.CANCELLED;
    case UnifiedAppointmentStatus.COMPLETED:
      return PrismaAppointmentStatus.COMPLETED;
    // NOTE: UnifiedAppointmentStatus.NO_SHOW is not mapped
    default:
      return PrismaAppointmentStatus.PENDING;
  }
};

export const fromPrismaStatus = (
  status: PrismaAppointmentStatus
): UnifiedAppointmentStatus => {
  switch (status) {
    case PrismaAppointmentStatus.PENDING:
      return UnifiedAppointmentStatus.PENDING;
    case PrismaAppointmentStatus.CONFIRMED:
      return UnifiedAppointmentStatus.CONFIRMED;
    case PrismaAppointmentStatus.CANCELLED:
      return UnifiedAppointmentStatus.CANCELLED;
    case PrismaAppointmentStatus.COMPLETED:
      return UnifiedAppointmentStatus.COMPLETED;
    case PrismaAppointmentStatus.RESCHEDULED:
      return UnifiedAppointmentStatus.RESCHEDULED;
    default:
      return UnifiedAppointmentStatus.PENDING;
  }
};

export function toPrismaAppointment(
  appointment: UnifiedAppointment
): PrismaAppointmentCreateInput {
  return {
    createdAt: appointment.createdAt,
    customerEmail: appointment.customerEmail,
    customerId: appointment.customerId,
    customerName: appointment.customerName,
    customerPhone: appointment.customerPhone,
    endTime: appointment.endTime,
    id: appointment.id,
    notes: appointment.notes,
    salonId: appointment.salonId,
    staffId: appointment.staffId,
    startTime: appointment.startTime,
    status: toPrismaStatus(appointment.status),
    treatmentId: appointment.treatmentId,
    updatedAt: appointment.updatedAt,
  };
}
