{"compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "bundler", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "allowJs": true, "strict": true, "forceConsistentCasingInFileNames": true, "skipLibCheck": true, "declaration": true, "declarationMap": true, "outDir": "./dist", "rootDir": "./src", "removeComments": false, "sourceMap": true, "resolveJsonModule": true, "isolatedModules": true, "noEmitOnError": true, "exactOptionalPropertyTypes": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": true, "noImplicitOverride": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"]}