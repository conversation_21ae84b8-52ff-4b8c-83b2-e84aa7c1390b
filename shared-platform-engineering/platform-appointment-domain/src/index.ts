/**
 * 🎯 APPOINTMENT DOMAIN LIBRARY - Extracted Business Logic
 *
 * This library contains all the appointment business logic extracted from
 * the appointment services before cleanup. It provides a clean, reusable
 * domain layer that can be used by any appointment service.
 *
 * @example
 * ```typescript
 * import {
 *   AppointmentDomain,
 *   AppointmentDomainService,
 *   AppointmentAggregate,
 *   createAppointmentDomain
 * } from '@beauty-crm/platform-appointment-domain';
 *
 * // Create appointment with business logic
 * const appointment = createAppointmentDomain(request);
 * appointment.confirm();
 *
 * // Use domain service for complex operations
 * const domainService = new AppointmentDomainService();
 * const conflicts = domainService.checkForConflicts(appointment, existing);
 *
 * // Use aggregate for command processing
 * const aggregate = new AppointmentAggregate();
 * const result = aggregate.processCommand(command, existingAppointments);
 * ```
 */

// ============================================================================
// 🎯 DOMAIN MODEL EXPORTS
// ============================================================================

export {
  AppointmentDomain,
  createAppointmentDomain,
  appointmentFromCore,
} from './domain/AppointmentDomain';

// ============================================================================
// 🎯 DOMAIN SERVICE EXPORTS
// ============================================================================

export {
  AppointmentDomainService,
  type TimeSlot,
  type AppointmentFilters,
  type AppointmentStats,
} from './services/AppointmentDomainService';

// ============================================================================
// 🎯 AGGREGATE EXPORTS
// ============================================================================

export {
  AppointmentAggregate,
  type BaseCommand,
  type CreateAppointmentCommand,
  type UpdateAppointmentCommand,
  type CancelAppointmentCommand,
  type ConfirmAppointmentCommand,
  type CompleteAppointmentCommand,
  type AppointmentCommand,
  type AppointmentOperationResult,
} from './aggregates/AppointmentAggregate';

// ============================================================================
// 🎯 UTILITY EXPORTS
// ============================================================================

export const DOMAIN_VERSION = '1.0.0';
export const DOMAIN_NAME = '@beauty-crm/platform-appointment-domain';

/**
 * 🎉 Welcome message for the domain package
 */
export function welcomeToDomain() {
  console.log(`
  🎯 Welcome to ${DOMAIN_NAME} v${DOMAIN_VERSION}
  
  Extracted Business Logic from Appointment Services
  Clean, reusable domain layer for appointment management!
  
  Features:
  - ✅ Appointment Domain Model with business rules
  - ✅ Domain Service for complex operations
  - ✅ Aggregate for command processing
  - ✅ Conflict detection and validation
  - ✅ Availability calculation
  - ✅ Analytics and reporting
  
  Happy coding! ✨
  `);
}
