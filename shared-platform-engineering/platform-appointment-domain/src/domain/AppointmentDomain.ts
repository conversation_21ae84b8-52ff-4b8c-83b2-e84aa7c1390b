/**
 * 🎯 APPOINTMENT DOMAIN MODEL - Extracted Business Logic
 *
 * This contains all the core business logic from the appointment services,
 * consolidated into a single, reusable domain model.
 */

import {
  Appointment,
  CreateAppointmentRequest,
  AppointmentStatus,
  AppointmentDomainModel,
  APPOINTMENT_STATUSES,
  APPOINTMENT_SOURCES,
} from '@beauty-crm/platform-appointment-schema';

import { createId } from '@paralleldrive/cuid2';

// ============================================================================
// 🎯 CORE APPOINTMENT DOMAIN MODEL
// ============================================================================

export class AppointmentDomain implements AppointmentDomainModel {
  private _appointment: Appointment;

  constructor(appointment: Appointment) {
    this._appointment = appointment;
  }

  // ============================================================================
  // 📋 PROPERTY ACCESSORS - Auto-derived from schema
  // ============================================================================

  get id() {
    return this._appointment.id;
  }
  get salonId() {
    return this._appointment.salonId;
  }
  get customerId() {
    return this._appointment.customerId;
  }
  get staffId() {
    return this._appointment.staffId;
  }
  get treatmentId() {
    return this._appointment.treatmentId;
  }

  get customerName() {
    return this._appointment.customerName;
  }
  get customerEmail() {
    return this._appointment.customerEmail;
  }
  get customerPhone() {
    return this._appointment.customerPhone;
  }

  get treatmentName() {
    return this._appointment.treatmentName;
  }
  get treatmentDuration() {
    return this._appointment.treatmentDuration;
  }
  get treatmentPrice() {
    return this._appointment.treatmentPrice;
  }

  get salonName() {
    return this._appointment.salonName;
  }
  get salonLogo() {
    return this._appointment.salonLogo;
  }
  get salonColor() {
    return this._appointment.salonColor;
  }

  get startTime() {
    return this._appointment.startTime;
  }
  get endTime() {
    return this._appointment.endTime;
  }

  get status() {
    return this._appointment.status;
  }
  get notes() {
    return this._appointment.notes;
  }
  get locale() {
    return this._appointment.locale;
  }

  get source() {
    return this._appointment.source;
  }
  get plannerAppointmentId() {
    return this._appointment.plannerAppointmentId;
  }

  get createdAt() {
    return this._appointment.createdAt;
  }
  get updatedAt() {
    return this._appointment.updatedAt;
  }
  get confirmedAt() {
    return this._appointment.confirmedAt;
  }
  get completedAt() {
    return this._appointment.completedAt;
  }
  get cancelledAt() {
    return this._appointment.cancelledAt;
  }

  // ============================================================================
  // 🎯 CORE BUSINESS LOGIC - Extracted from services
  // ============================================================================

  confirm(): void {
    if (this.status !== APPOINTMENT_STATUSES.PENDING) {
      throw new Error(`Cannot confirm appointment with status: ${this.status}`);
    }

    this._appointment = {
      ...this._appointment,
      status: APPOINTMENT_STATUSES.CONFIRMED,
      confirmedAt: new Date(),
      updatedAt: new Date(),
    };
  }

  cancel(reason?: string): void {
    if (!this.canBeModified()) {
      throw new Error(`Cannot cancel appointment with status: ${this.status}`);
    }

    this._appointment = {
      ...this._appointment,
      status: APPOINTMENT_STATUSES.CANCELLED,
      cancelledAt: new Date(),
      updatedAt: new Date(),
      notes: reason
        ? `${this.notes || ''}\nCancellation reason: ${reason}`.trim()
        : this.notes,
    };
  }

  complete(): void {
    if (
      this.status !== APPOINTMENT_STATUSES.CONFIRMED &&
      this.status !== APPOINTMENT_STATUSES.IN_PROGRESS
    ) {
      throw new Error(
        `Cannot complete appointment with status: ${this.status}`
      );
    }

    this._appointment = {
      ...this._appointment,
      status: APPOINTMENT_STATUSES.COMPLETED,
      completedAt: new Date(),
      updatedAt: new Date(),
    };
  }

  reschedule(newStartTime: Date, newEndTime: Date): void {
    if (!this.canBeModified()) {
      throw new Error(
        `Cannot reschedule appointment with status: ${this.status}`
      );
    }

    if (newStartTime >= newEndTime) {
      throw new Error('Start time must be before end time');
    }

    this._appointment = {
      ...this._appointment,
      startTime: newStartTime,
      endTime: newEndTime,
      status: APPOINTMENT_STATUSES.RESCHEDULED,
      updatedAt: new Date(),
    };
  }

  // ============================================================================
  // 🔍 BUSINESS RULE VALIDATIONS
  // ============================================================================

  isOverlapping(other: AppointmentDomainModel): boolean {
    return (
      this.startTime < other.endTime &&
      this.endTime > other.startTime &&
      this.salonId === other.salonId &&
      this.staffId === other.staffId &&
      this.id !== other.id
    );
  }

  canBeModified(): boolean {
    const nonModifiableStatuses = [
      APPOINTMENT_STATUSES.COMPLETED,
      APPOINTMENT_STATUSES.CANCELLED,
      APPOINTMENT_STATUSES.NO_SHOW,
    ] as const;
    return !nonModifiableStatuses.includes(
      this.status as typeof nonModifiableStatuses[number]
    );
  }

  getDuration(): number {
    return this.treatmentDuration;
  }

  getTimeSlot(): { start: Date; end: Date } {
    return {
      start: this.startTime,
      end: this.endTime,
    };
  }

  // ============================================================================
  // 🏪 SALON-SPECIFIC BUSINESS LOGIC
  // ============================================================================

  isWithinOperatingHours(operatingHours: {
    open: string;
    close: string;
  }): boolean {
    const startHour = this.startTime.getHours();
    const endHour = this.endTime.getHours();
    const openHour = parseInt(operatingHours.open.split(':')[0] || '9');
    const closeHour = parseInt(operatingHours.close.split(':')[0] || '18');

    return startHour >= openHour && endHour <= closeHour;
  }

  getBufferTime(): { before: number; after: number } {
    const baseBuffer = 15; // 15 minutes default
    return {
      before: baseBuffer,
      after: baseBuffer,
    };
  }

  isEligibleForOnlineBooking(): boolean {
    const now = new Date();
    const hoursUntilAppointment =
      (this.startTime.getTime() - now.getTime()) / (1000 * 60 * 60);
    return hoursUntilAppointment >= 2; // Must be at least 2 hours in advance
  }

  getBookingConfirmation(): {
    confirmationNumber: string;
    customerMessage: string;
    salonMessage: string;
  } {
    return {
      confirmationNumber: this.id.slice(-8).toUpperCase(),
      customerMessage: `Your appointment for ${
        this.treatmentName
      } is confirmed for ${this.startTime.toLocaleDateString()} at ${this.startTime.toLocaleTimeString()}.`,
      salonMessage: `New appointment: ${this.customerName} - ${
        this.treatmentName
      } on ${this.startTime.toLocaleDateString()} at ${this.startTime.toLocaleTimeString()}.`,
    };
  }

  // ============================================================================
  // 🔄 CONVERSION METHODS
  // ============================================================================

  toAppointment(): Appointment {
    return this._appointment;
  }

  static fromCreateRequest(
    request: CreateAppointmentRequest
  ): AppointmentDomain {
    const appointment: Appointment = {
      id: createId(),
      ...request,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    return new AppointmentDomain(appointment);
  }

  static fromAppointment(appointment: Appointment): AppointmentDomain {
    return new AppointmentDomain(appointment);
  }
}

// ============================================================================
// 🏭 FACTORY FUNCTIONS
// ============================================================================

export const createAppointmentDomain = (
  request: CreateAppointmentRequest
): AppointmentDomain => {
  return AppointmentDomain.fromCreateRequest(request);
};

export const appointmentFromCore = (
  appointment: Appointment
): AppointmentDomain => {
  return AppointmentDomain.fromAppointment(appointment);
};
