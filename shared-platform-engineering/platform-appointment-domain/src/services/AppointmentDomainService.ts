/**
 * 🎯 APPOINTMENT DOMAIN SERVICE - Extracted Business Logic
 * 
 * This contains all the domain services and business rules extracted
 * from the appointment services, including conflict detection,
 * scheduling logic, and availability calculations.
 */

import {
  Appointment,
  CreateAppointmentRequest,
  AppointmentStatus,
  APPOINTMENT_STATUSES,
} from '@beauty-crm/platform-appointment-schema';

import { AppointmentDomain } from '../domain/AppointmentDomain';

// ============================================================================
// 🎯 DOMAIN SERVICE INTERFACES
// ============================================================================

export interface TimeSlot {
  start: Date;
  end: Date;
  available: boolean;
}

export interface AppointmentFilters {
  status?: AppointmentStatus;
  staffId?: string;
  treatmentId?: string;
  startDate?: Date;
  endDate?: Date;
}

export interface AppointmentStats {
  total: number;
  confirmed: number;
  completed: number;
  cancelled: number;
  revenue: number;
}

// ============================================================================
// 🎯 APPOINTMENT DOMAIN SERVICE
// ============================================================================

export class AppointmentDomainService {
  
  // ============================================================================
  // 🔍 CONFLICT DETECTION - Extracted from services
  // ============================================================================

  /**
   * Check if appointments overlap (extracted from AppointmentService)
   */
  checkForConflicts(
    newAppointment: AppointmentDomain,
    existingAppointments: AppointmentDomain[]
  ): AppointmentDomain[] {
    return existingAppointments.filter(existing => 
      newAppointment.isOverlapping(existing)
    );
  }

  /**
   * Validate appointment doesn't conflict with existing ones
   */
  validateNoConflicts(
    newAppointment: AppointmentDomain,
    existingAppointments: AppointmentDomain[]
  ): void {
    const conflicts = this.checkForConflicts(newAppointment, existingAppointments);
    
    if (conflicts.length > 0) {
      throw new Error(
        `The staff member already has an appointment during this time slot. Conflicting appointments: ${conflicts.map(c => c.id).join(', ')}`
      );
    }
  }

  // ============================================================================
  // 📅 AVAILABILITY CALCULATION - Extracted from repositories
  // ============================================================================

  /**
   * Generate available time slots for a given day
   */
  generateAvailableSlots(
    date: Date,
    duration: number,
    existingAppointments: AppointmentDomain[],
    operatingHours: { start: number; end: number } = { start: 9, end: 18 }
  ): TimeSlot[] {
    const slots: TimeSlot[] = [];
    const slotInterval = 30; // 30-minute intervals
    
    for (let hour = operatingHours.start; hour < operatingHours.end; hour++) {
      for (let minute = 0; minute < 60; minute += slotInterval) {
        const slotStart = new Date(date);
        slotStart.setHours(hour, minute, 0, 0);
        
        const slotEnd = new Date(slotStart);
        slotEnd.setMinutes(slotEnd.getMinutes() + duration);

        // Check if slot conflicts with existing appointments
        const isAvailable = !existingAppointments.some(appointment => {
          return (
            slotStart < appointment.endTime && 
            slotEnd > appointment.startTime &&
            appointment.status !== APPOINTMENT_STATUSES.CANCELLED
          );
        });

        slots.push({
          start: slotStart,
          end: slotEnd,
          available: isAvailable,
        });
      }
    }

    return slots;
  }

  /**
   * Find next available slot for a treatment
   */
  findNextAvailableSlot(
    startDate: Date,
    duration: number,
    existingAppointments: AppointmentDomain[],
    daysToSearch: number = 30
  ): TimeSlot | null {
    for (let day = 0; day < daysToSearch; day++) {
      const searchDate = new Date(startDate);
      searchDate.setDate(searchDate.getDate() + day);
      
      const slots = this.generateAvailableSlots(searchDate, duration, existingAppointments);
      const availableSlot = slots.find(slot => slot.available);
      
      if (availableSlot) {
        return availableSlot;
      }
    }
    
    return null;
  }

  // ============================================================================
  // 📊 ANALYTICS & REPORTING - Extracted from repositories
  // ============================================================================

  /**
   * Calculate appointment statistics
   */
  calculateStats(appointments: AppointmentDomain[]): AppointmentStats {
    return {
      total: appointments.length,
      confirmed: appointments.filter(a => a.status === APPOINTMENT_STATUSES.CONFIRMED).length,
      completed: appointments.filter(a => a.status === APPOINTMENT_STATUSES.COMPLETED).length,
      cancelled: appointments.filter(a => a.status === APPOINTMENT_STATUSES.CANCELLED).length,
      revenue: appointments
        .filter(a => a.status === APPOINTMENT_STATUSES.COMPLETED)
        .reduce((sum, a) => sum + a.treatmentPrice, 0),
    };
  }

  /**
   * Get appointments by status
   */
  filterByStatus(appointments: AppointmentDomain[], status: AppointmentStatus): AppointmentDomain[] {
    return appointments.filter(appointment => appointment.status === status);
  }

  /**
   * Get appointments for today
   */
  getTodaysAppointments(appointments: AppointmentDomain[]): AppointmentDomain[] {
    const today = new Date();
    const startOfDay = new Date(today);
    startOfDay.setHours(0, 0, 0, 0);
    
    const endOfDay = new Date(today);
    endOfDay.setHours(23, 59, 59, 999);

    return appointments.filter(appointment => 
      appointment.startTime >= startOfDay && appointment.startTime <= endOfDay
    );
  }

  /**
   * Get upcoming appointments
   */
  getUpcomingAppointments(appointments: AppointmentDomain[], days: number = 7): AppointmentDomain[] {
    const now = new Date();
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + days);

    return appointments.filter(appointment => 
      appointment.startTime >= now && appointment.startTime <= futureDate
    );
  }

  // ============================================================================
  // 🔄 APPOINTMENT LIFECYCLE - Extracted from services
  // ============================================================================

  /**
   * Process appointment creation with business rules
   */
  processAppointmentCreation(
    request: CreateAppointmentRequest,
    existingAppointments: AppointmentDomain[]
  ): AppointmentDomain {
    // Create domain object
    const appointment = AppointmentDomain.fromCreateRequest(request);
    
    // Validate business rules
    this.validateAppointmentCreation(appointment, existingAppointments);
    
    return appointment;
  }

  /**
   * Validate appointment creation business rules
   */
  private validateAppointmentCreation(
    appointment: AppointmentDomain,
    existingAppointments: AppointmentDomain[]
  ): void {
    // Check for conflicts
    this.validateNoConflicts(appointment, existingAppointments);
    
    // Validate time slot
    if (appointment.startTime >= appointment.endTime) {
      throw new Error('Start time must be before end time');
    }
    
    // Validate future booking
    if (appointment.startTime <= new Date()) {
      throw new Error('Appointment must be scheduled for a future time');
    }
    
    // Validate duration
    if (appointment.getDuration() <= 0) {
      throw new Error('Treatment duration must be positive');
    }
    
    // Validate price
    if (appointment.treatmentPrice <= 0) {
      throw new Error('Treatment price must be positive');
    }
  }

  // ============================================================================
  // 🔍 SEARCH & FILTERING - Extracted from repositories
  // ============================================================================

  /**
   * Filter appointments by multiple criteria
   */
  filterAppointments(
    appointments: AppointmentDomain[],
    filters: AppointmentFilters
  ): AppointmentDomain[] {
    return appointments.filter(appointment => {
      if (filters.status && appointment.status !== filters.status) {
        return false;
      }
      
      if (filters.staffId && appointment.staffId !== filters.staffId) {
        return false;
      }
      
      if (filters.treatmentId && appointment.treatmentId !== filters.treatmentId) {
        return false;
      }
      
      if (filters.startDate && appointment.startTime < filters.startDate) {
        return false;
      }
      
      if (filters.endDate && appointment.startTime > filters.endDate) {
        return false;
      }
      
      return true;
    });
  }

  /**
   * Sort appointments by start time
   */
  sortByStartTime(appointments: AppointmentDomain[], ascending: boolean = true): AppointmentDomain[] {
    return [...appointments].sort((a, b) => {
      const diff = a.startTime.getTime() - b.startTime.getTime();
      return ascending ? diff : -diff;
    });
  }
}
