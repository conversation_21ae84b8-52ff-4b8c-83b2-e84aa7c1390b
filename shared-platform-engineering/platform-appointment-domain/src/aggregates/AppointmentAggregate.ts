/**
 * 🎯 APPOINTMENT AGGREGATE - Extracted from Services
 *
 * This contains the appointment aggregate logic extracted from the
 * appointment services, including command handling and event generation.
 */

import {
  Appointment,
  CreateAppointmentRequest,
  UpdateAppointmentRequest,
  createAppointmentCreatedEvent,
  createAppointmentUpdatedEvent,
  createAppointmentCancelledEvent,
  createAppointmentConfirmedEvent,
  createAppointmentCompletedEvent,
  AppointmentEvent,
} from '@beauty-crm/platform-appointment-schema';

import { AppointmentDomain } from '../domain/AppointmentDomain';
import { AppointmentDomainService } from '../services/AppointmentDomainService';

// ============================================================================
// 🎯 COMMAND TYPES - Extracted from services
// ============================================================================

export interface BaseCommand {
  commandId: string;
  commandType: string;
  aggregateId: string;
  timestamp: Date;
  userId?: string;
  correlationId?: string;
}

export interface CreateAppointmentCommand extends BaseCommand {
  commandType: 'create-appointment';
  data: CreateAppointmentRequest;
}

export interface UpdateAppointmentCommand extends BaseCommand {
  commandType: 'update-appointment';
  data: UpdateAppointmentRequest;
}

export interface CancelAppointmentCommand extends BaseCommand {
  commandType: 'cancel-appointment';
  data: {
    reason?: string;
    cancelledBy?: string;
  };
}

export interface ConfirmAppointmentCommand extends BaseCommand {
  commandType: 'confirm-appointment';
  data: {
    confirmedBy?: string;
    confirmationMethod?: 'email' | 'sms' | 'phone' | 'in_person';
  };
}

export interface CompleteAppointmentCommand extends BaseCommand {
  commandType: 'complete-appointment';
  data: {
    completedBy?: string;
    actualDuration?: number;
    customerSatisfaction?: number;
    notes?: string;
  };
}

export type AppointmentCommand =
  | CreateAppointmentCommand
  | UpdateAppointmentCommand
  | CancelAppointmentCommand
  | ConfirmAppointmentCommand
  | CompleteAppointmentCommand;

// ============================================================================
// 🎯 AGGREGATE RESULT TYPES
// ============================================================================

export interface AppointmentOperationResult {
  success: boolean;
  appointment?: Appointment;
  events: AppointmentEvent[];
  error?: string;
}

// ============================================================================
// 🎯 APPOINTMENT AGGREGATE
// ============================================================================

export class AppointmentAggregate {
  private appointment: AppointmentDomain | null = null;
  private domainService: AppointmentDomainService;
  private uncommittedEvents: AppointmentEvent[] = [];

  constructor(
    appointment?: Appointment,
    domainService?: AppointmentDomainService
  ) {
    if (appointment) {
      this.appointment = AppointmentDomain.fromAppointment(appointment);
    }
    this.domainService = domainService || new AppointmentDomainService();
  }

  // ============================================================================
  // 🎯 COMMAND HANDLERS - Extracted from services
  // ============================================================================

  processCommand(
    command: AppointmentCommand,
    existingAppointments: AppointmentDomain[] = []
  ): AppointmentOperationResult {
    try {
      this.validateCommand(command);

      switch (command.commandType) {
        case 'create-appointment':
          return this.handleCreateAppointment(command, existingAppointments);
        case 'update-appointment':
          return this.handleUpdateAppointment(command);
        case 'cancel-appointment':
          return this.handleCancelAppointment(command);
        case 'confirm-appointment':
          return this.handleConfirmAppointment(command);
        case 'complete-appointment':
          return this.handleCompleteAppointment(command);
        default:
          throw new Error(
            `Unknown command type: ${
              (command as AppointmentCommand).commandType
            }`
          );
      }
    } catch (error) {
      return {
        success: false,
        events: [],
        error:
          error instanceof Error ? error.message : 'Unknown error occurred',
      };
    }
  }

  private handleCreateAppointment(
    command: CreateAppointmentCommand,
    existingAppointments: AppointmentDomain[]
  ): AppointmentOperationResult {
    // Use domain service to process creation with business rules
    const appointmentDomain = this.domainService.processAppointmentCreation(
      command.data,
      existingAppointments
    );

    this.appointment = appointmentDomain;

    // Generate event
    const event = createAppointmentCreatedEvent(
      appointmentDomain.toAppointment(),
      {
        source: 'appointment-aggregate',
        ...(command.correlationId && { correlationId: command.correlationId }),
        ...(command.userId && { userId: command.userId }),
      }
    );

    this.uncommittedEvents.push(event);

    return {
      success: true,
      appointment: appointmentDomain.toAppointment(),
      events: [event],
    };
  }

  private handleUpdateAppointment(
    command: UpdateAppointmentCommand
  ): AppointmentOperationResult {
    if (!this.appointment) {
      throw new Error('Cannot update non-existent appointment');
    }

    const previousAppointment = this.appointment.toAppointment();

    // Apply updates - only include defined fields
    const updatedData = { ...previousAppointment };
    Object.entries(command.data).forEach(([key, value]) => {
      if (value !== undefined) {
        (updatedData as any)[key] = value;
      }
    });
    this.appointment = AppointmentDomain.fromAppointment(updatedData);

    // Generate event
    const eventOptions = {
      source: 'appointment-aggregate' as const,
      previousValues: previousAppointment,
    } as any;
    if (command.correlationId)
      eventOptions.correlationId = command.correlationId;
    if (command.userId) eventOptions.userId = command.userId;

    // Filter out undefined values from command.data
    const filteredData = Object.fromEntries(
      Object.entries(command.data).filter(([_, value]) => value !== undefined)
    );

    const event = createAppointmentUpdatedEvent(
      this.appointment.toAppointment(),
      filteredData,
      eventOptions
    );

    this.uncommittedEvents.push(event);

    return {
      success: true,
      appointment: this.appointment.toAppointment(),
      events: [event],
    };
  }

  private handleCancelAppointment(
    command: CancelAppointmentCommand
  ): AppointmentOperationResult {
    if (!this.appointment) {
      throw new Error('Cannot cancel non-existent appointment');
    }

    // Use domain logic for cancellation
    this.appointment.cancel(command.data.reason);

    // Generate event
    const event = createAppointmentCancelledEvent(this.appointment.id, {
      source: 'appointment-aggregate',
      ...(command.data.reason && { reason: command.data.reason }),
      ...(command.data.cancelledBy && {
        cancelledBy: command.data.cancelledBy,
      }),
      ...(command.correlationId && { correlationId: command.correlationId }),
      ...(command.userId && { userId: command.userId }),
    });

    this.uncommittedEvents.push(event);

    return {
      success: true,
      appointment: this.appointment.toAppointment(),
      events: [event],
    };
  }

  private handleConfirmAppointment(
    command: ConfirmAppointmentCommand
  ): AppointmentOperationResult {
    if (!this.appointment) {
      throw new Error('Cannot confirm non-existent appointment');
    }

    // Use domain logic for confirmation
    this.appointment.confirm();

    // Generate event
    const confirmOptions = { source: 'appointment-aggregate' as const } as any;
    if (command.data.confirmedBy)
      confirmOptions.confirmedBy = command.data.confirmedBy;
    if (command.data.confirmationMethod)
      confirmOptions.confirmationMethod = command.data.confirmationMethod;
    if (command.correlationId)
      confirmOptions.correlationId = command.correlationId;
    if (command.userId) confirmOptions.userId = command.userId;

    const event = createAppointmentConfirmedEvent(
      this.appointment.id,
      confirmOptions
    );

    this.uncommittedEvents.push(event);

    return {
      success: true,
      appointment: this.appointment.toAppointment(),
      events: [event],
    };
  }

  private handleCompleteAppointment(
    command: CompleteAppointmentCommand
  ): AppointmentOperationResult {
    if (!this.appointment) {
      throw new Error('Cannot complete non-existent appointment');
    }

    // Use domain logic for completion
    this.appointment.complete();

    // Generate event
    const completeOptions = { source: 'appointment-aggregate' as const } as any;
    if (command.data.completedBy)
      completeOptions.completedBy = command.data.completedBy;
    if (command.data.actualDuration)
      completeOptions.actualDuration = command.data.actualDuration;
    if (command.data.customerSatisfaction)
      completeOptions.customerSatisfaction = command.data.customerSatisfaction;
    if (command.data.notes) completeOptions.notes = command.data.notes;
    if (command.correlationId)
      completeOptions.correlationId = command.correlationId;
    if (command.userId) completeOptions.userId = command.userId;

    const event = createAppointmentCompletedEvent(
      this.appointment.id,
      completeOptions
    );

    this.uncommittedEvents.push(event);

    return {
      success: true,
      appointment: this.appointment.toAppointment(),
      events: [event],
    };
  }

  // ============================================================================
  // 🔍 VALIDATION & UTILITIES
  // ============================================================================

  private validateCommand(command: AppointmentCommand): void {
    if (!command.commandId) {
      throw new Error('Command must have a commandId');
    }

    if (!command.commandType) {
      throw new Error('Command must have a commandType');
    }

    if (!command.timestamp) {
      throw new Error('Command must have a timestamp');
    }
  }

  // ============================================================================
  // 🎯 AGGREGATE STATE MANAGEMENT
  // ============================================================================

  getUncommittedEvents(): AppointmentEvent[] {
    return [...this.uncommittedEvents];
  }

  markEventsAsCommitted(): void {
    this.uncommittedEvents = [];
  }

  getCurrentState(): Appointment | null {
    return this.appointment?.toAppointment() || null;
  }

  hasUncommittedChanges(): boolean {
    return this.uncommittedEvents.length > 0;
  }
}
