import { beforeEach, describe, expect, it, vi } from 'vitest';
import type { EventPublisher } from '@beauty-crm/platform-eventing';
import type { Appointment } from '@beauty-crm/product-appointment-types/src/appointment/types';
import { AppointmentEventPublisher } from '../AppointmentEventPublisher';
import * as events from '../events';

// Mock the events module
vi.mock('../events', () => ({
  createAppointmentCreatedEvent: vi.fn(),
  createAppointmentUpdatedEvent: vi.fn(),
  createAppointmentCancelledEvent: vi.fn(),
}));

// Mock appointment data for testing
const mockAppointment: Appointment = {
  id: 'appointment-123',
  salonId: 'salon-456',
  customerId: 'customer-789',
  treatmentId: 'treatment-101',
  staffId: 'staff-202',
  timeSlot: {
    start: new Date('2024-01-15T10:00:00Z'),
    end: new Date('2024-01-15T11:00:00Z'),
  },
  status: 'CONFIRMED',
  priority: 'medium',
  notes: 'Regular appointment',
  createdAt: new Date('2024-01-01T00:00:00Z'),
  updatedAt: new Date('2024-01-01T00:00:00Z'),
};

const mockEventOptions = {
  source: 'appointment-service',
  correlationId: 'corr-123',
  userId: 'user-456',
};

const mockEvent = {
  eventId: 'event-123',
  eventType: 'appointment.created',
  aggregateId: 'appointment-123',
  aggregateType: 'appointment',
  data: mockAppointment,
  timestamp: '2024-01-01T00:00:00.000Z',
  eventVersion: 1,
  source: 'appointment-service',
  metadata: {},
};

describe('AppointmentEventPublisher', () => {
  let appointmentEventPublisher: AppointmentEventPublisher;
  let mockEventPublisher: EventPublisher;

  beforeEach(() => {
    mockEventPublisher = {
      publish: vi.fn(),
      connect: vi.fn(),
      disconnect: vi.fn(),
    } as any;

    appointmentEventPublisher = new AppointmentEventPublisher(
      mockEventPublisher,
    );
    vi.clearAllMocks();
  });

  describe('publishAppointmentCreated', () => {
    it('should create and publish an appointment created event', async () => {
      const createEventSpy = vi.mocked(events.createAppointmentCreatedEvent);
      createEventSpy.mockReturnValue(mockEvent as any);

      await appointmentEventPublisher.publishAppointmentCreated(
        mockAppointment,
        mockEventOptions,
      );

      expect(createEventSpy).toHaveBeenCalledWith(
        mockAppointment,
        mockEventOptions,
      );
      expect(mockEventPublisher.publish).toHaveBeenCalledWith(mockEvent);
    });

    it('should handle publishing errors gracefully', async () => {
      const createEventSpy = vi.mocked(events.createAppointmentCreatedEvent);
      createEventSpy.mockReturnValue(mockEvent as any);

      const publishError = new Error('Publishing failed');
      vi.mocked(mockEventPublisher.publish).mockRejectedValue(publishError);

      await expect(
        appointmentEventPublisher.publishAppointmentCreated(
          mockAppointment,
          mockEventOptions,
        ),
      ).rejects.toThrow('Publishing failed');
    });

    it('should work with minimal event options', async () => {
      const createEventSpy = vi.mocked(events.createAppointmentCreatedEvent);
      createEventSpy.mockReturnValue(mockEvent as any);

      const minimalOptions = { source: 'test-service' };

      await appointmentEventPublisher.publishAppointmentCreated(
        mockAppointment,
        minimalOptions,
      );

      expect(createEventSpy).toHaveBeenCalledWith(
        mockAppointment,
        minimalOptions,
      );
      expect(mockEventPublisher.publish).toHaveBeenCalledWith(mockEvent);
    });
  });

  describe('publishAppointmentUpdated', () => {
    it('should create and publish an appointment updated event', async () => {
      const changes: Partial<Appointment> = {
        status: 'COMPLETED',
        notes: 'Updated notes',
      };

      const updatedEvent = {
        ...mockEvent,
        eventType: 'appointment.updated',
      };

      const createEventSpy = vi.mocked(events.createAppointmentUpdatedEvent);
      createEventSpy.mockReturnValue(updatedEvent as any);

      await appointmentEventPublisher.publishAppointmentUpdated(
        mockAppointment,
        changes,
        mockEventOptions,
      );

      expect(createEventSpy).toHaveBeenCalledWith(
        mockAppointment,
        changes,
        mockEventOptions,
      );
      expect(mockEventPublisher.publish).toHaveBeenCalledWith(updatedEvent);
    });

    it('should handle empty changes', async () => {
      const createEventSpy = vi.mocked(events.createAppointmentUpdatedEvent);
      createEventSpy.mockReturnValue(mockEvent as any);

      await appointmentEventPublisher.publishAppointmentUpdated(
        mockAppointment,
        {},
        mockEventOptions,
      );

      expect(createEventSpy).toHaveBeenCalledWith(
        mockAppointment,
        {},
        mockEventOptions,
      );
    });

    it('should handle publishing errors', async () => {
      const createEventSpy = vi.mocked(events.createAppointmentUpdatedEvent);
      createEventSpy.mockReturnValue(mockEvent as any);

      const publishError = new Error('Network error');
      vi.mocked(mockEventPublisher.publish).mockRejectedValue(publishError);

      await expect(
        appointmentEventPublisher.publishAppointmentUpdated(
          mockAppointment,
          { status: 'CANCELLED' },
          mockEventOptions,
        ),
      ).rejects.toThrow('Network error');
    });
  });

  describe('publishAppointmentCancelled', () => {
    it('should create and publish an appointment cancelled event', async () => {
      const appointmentId = 'appointment-123';
      const reason = 'Customer requested cancellation';

      const cancelledEvent = {
        ...mockEvent,
        eventType: 'appointment.cancelled',
        data: { appointmentId, reason },
      };

      const createEventSpy = vi.mocked(events.createAppointmentCancelledEvent);
      createEventSpy.mockReturnValue(cancelledEvent as any);

      await appointmentEventPublisher.publishAppointmentCancelled(
        appointmentId,
        reason,
        mockEventOptions,
      );

      expect(createEventSpy).toHaveBeenCalledWith(
        appointmentId,
        reason,
        mockEventOptions,
      );
      expect(mockEventPublisher.publish).toHaveBeenCalledWith(cancelledEvent);
    });

    it('should handle different cancellation reasons', async () => {
      const appointmentId = 'appointment-456';
      const reason = 'Staff unavailable';

      const createEventSpy = vi.mocked(events.createAppointmentCancelledEvent);
      createEventSpy.mockReturnValue(mockEvent as any);

      await appointmentEventPublisher.publishAppointmentCancelled(
        appointmentId,
        reason,
        mockEventOptions,
      );

      expect(createEventSpy).toHaveBeenCalledWith(
        appointmentId,
        reason,
        mockEventOptions,
      );
    });

    it('should handle publishing errors', async () => {
      const createEventSpy = vi.mocked(events.createAppointmentCancelledEvent);
      createEventSpy.mockReturnValue(mockEvent as any);

      const publishError = new Error('Connection timeout');
      vi.mocked(mockEventPublisher.publish).mockRejectedValue(publishError);

      await expect(
        appointmentEventPublisher.publishAppointmentCancelled(
          'appointment-123',
          'test reason',
          mockEventOptions,
        ),
      ).rejects.toThrow('Connection timeout');
    });
  });

  describe('Integration', () => {
    it('should use the same event publisher instance for all methods', async () => {
      const createCreatedSpy = vi.mocked(events.createAppointmentCreatedEvent);
      const createUpdatedSpy = vi.mocked(events.createAppointmentUpdatedEvent);
      const createCancelledSpy = vi.mocked(
        events.createAppointmentCancelledEvent,
      );

      createCreatedSpy.mockReturnValue(mockEvent as any);
      createUpdatedSpy.mockReturnValue(mockEvent as any);
      createCancelledSpy.mockReturnValue(mockEvent as any);

      await appointmentEventPublisher.publishAppointmentCreated(
        mockAppointment,
        mockEventOptions,
      );
      await appointmentEventPublisher.publishAppointmentUpdated(
        mockAppointment,
        { status: 'COMPLETED' },
        mockEventOptions,
      );
      await appointmentEventPublisher.publishAppointmentCancelled(
        'appointment-123',
        'test reason',
        mockEventOptions,
      );

      expect(mockEventPublisher.publish).toHaveBeenCalledTimes(3);
    });
  });
});
