import { beforeEach, describe, expect, it, vi } from 'vitest';
import { EventPublisher } from '@beauty-crm/platform-eventing';
import type { UnifiedAppointment } from '@beauty-crm/product-appointment-types';
import { UnifiedAppointmentStatus } from '@beauty-crm/product-appointment-types';
import { AppointmentEventPublisher } from '../AppointmentEventPublisher';
import { PrismaTransactionalOutbox } from '../outbox/TransactionalOutbox';
import { mockJetStreamClient } from './setup';

// Mock Prisma Client for integration tests
const mockPrismaClient = {
  $executeRawUnsafe: vi.fn(),
  $queryRawUnsafe: vi.fn(),
  $transaction: vi.fn((callback: any) => callback(mockPrismaClient)),
} as any;

// Mock appointment data
const mockAppointment: UnifiedAppointment = {
  id: 'appointment-integration-test',
  salonId: 'salon-456',
  customerId: 'customer-789',
  customerName: '<PERSON>',
  customerEmail: '<EMAIL>',
  treatmentId: 'treatment-101',
  treatmentName: 'Manicure',
  treatmentDuration: 60,
  treatmentPrice: 40,
  staffId: 'staff-202',
  startTime: new Date('2024-01-15T10:00:00Z'),
  endTime: new Date('2024-01-15T11:00:00Z'),
  status: UnifiedAppointmentStatus.CONFIRMED,
  source: 'PLANNER',
  notes: 'Integration test appointment',
  createdAt: new Date('2024-01-01T00:00:00Z'),
  updatedAt: new Date('2024-01-01T00:00:00Z'),
};

const mockEventOptions = {
  source: 'appointment-integration-test',
  correlationId: 'integration-corr-123',
  userId: 'integration-user-456',
};

describe('Integration Tests', () => {
  let eventPublisher: EventPublisher;
  let appointmentEventPublisher: AppointmentEventPublisher;
  let transactionalOutbox: PrismaTransactionalOutbox;

  beforeEach(() => {
    // Create real EventPublisher with mock config
    eventPublisher = new EventPublisher({
      serviceName: 'integration-test-service',
      stream: {
        name: 'INTEGRATION_TEST_EVENTS',
        subjects: ['integration.events.*'],
        description: 'Integration test events',
      },
    });

    appointmentEventPublisher = new AppointmentEventPublisher(eventPublisher);
    transactionalOutbox = new PrismaTransactionalOutbox(mockPrismaClient);

    vi.clearAllMocks();
  });

  describe('End-to-End Event Publishing', () => {
    it('should publish appointment created event through the full stack', async () => {
      // Create a mock event publisher to avoid real NATS connection
      const mockEventPublisher = {
        publish: vi.fn().mockResolvedValue(undefined),
        connect: vi.fn().mockResolvedValue(undefined),
        disconnect: vi.fn().mockResolvedValue(undefined),
      } as any;

      const testAppointmentEventPublisher = new AppointmentEventPublisher(
        mockEventPublisher
      );

      // Publish an appointment created event
      await testAppointmentEventPublisher.publishAppointmentCreated(
        mockAppointment,
        mockEventOptions
      );

      // Verify the event was published
      expect(mockEventPublisher.publish).toHaveBeenCalledWith(
        expect.objectContaining({
          eventType: 'appointment.created',
          aggregateId: mockAppointment.id,
          aggregateType: 'appointment',
          data: expect.objectContaining({
            appointment: expect.objectContaining({
              id: mockAppointment.id,
              salonId: mockAppointment.salonId,
              customerId: mockAppointment.customerId,
            }),
          }),
          source: mockEventOptions.source,
          correlationId: mockEventOptions.correlationId,
          userId: mockEventOptions.userId,
        })
      );
    });

    it('should publish appointment updated event with changes tracking', async () => {
      const mockEventPublisher = {
        publish: vi.fn().mockResolvedValue(undefined),
      } as any;

      const testAppointmentEventPublisher = new AppointmentEventPublisher(
        mockEventPublisher
      );

      const changes: Partial<UnifiedAppointment> = {
        status: UnifiedAppointmentStatus.COMPLETED,
        notes: 'Updated through integration test',
      };

      await testAppointmentEventPublisher.publishAppointmentUpdated(
        mockAppointment,
        changes,
        mockEventOptions
      );

      expect(mockEventPublisher.publish).toHaveBeenCalledWith(
        expect.objectContaining({
          eventType: 'appointment.updated',
          aggregateId: mockAppointment.id,
          data: expect.objectContaining({
            appointment: mockAppointment,
            changes: changes,
          }),
        })
      );
    });

    it('should publish appointment cancelled event', async () => {
      const mockEventPublisher = {
        publish: vi.fn().mockResolvedValue(undefined),
      } as any;

      const testAppointmentEventPublisher = new AppointmentEventPublisher(
        mockEventPublisher
      );

      const appointmentId = 'appointment-to-cancel';
      const reason = 'Integration test cancellation';

      await testAppointmentEventPublisher.publishAppointmentCancelled(
        appointmentId,
        reason,
        mockEventOptions
      );

      expect(mockEventPublisher.publish).toHaveBeenCalledWith(
        expect.objectContaining({
          eventType: 'appointment.cancelled',
          aggregateId: appointmentId,
          data: {
            appointmentId,
            reason,
          },
        })
      );
    });
  });

  describe('Transactional Outbox Integration', () => {
    it('should store and process events through outbox pattern', async () => {
      // Create a mock event
      const mockEvent = {
        eventId: 'integration-event-123',
        eventType: 'appointment.created',
        aggregateId: mockAppointment.id,
        aggregateType: 'appointment',
        data: mockAppointment,
        timestamp: new Date().toISOString(),
        eventVersion: 1,
        source: mockEventOptions.source,
        metadata: mockEventOptions,
      };

      // Store event in outbox
      await transactionalOutbox.add(mockEvent as any, mockPrismaClient);

      // Verify event was stored
      expect(mockPrismaClient.$executeRawUnsafe).toHaveBeenCalledWith(
        expect.stringContaining('INSERT INTO outbox_events'),
        expect.any(String), // UUID
        mockEvent.aggregateId,
        mockEvent.eventType,
        JSON.stringify(mockEvent),
        expect.any(Date),
        0, // retry_count
        'PENDING'
      );

      // Mock pending events for processing
      vi.mocked(mockPrismaClient.$queryRawUnsafe).mockResolvedValue([
        {
          id: 'outbox-integration-1',
          payload: JSON.stringify(mockEvent),
          retryCount: 0,
        },
      ]);

      // Process pending events
      const processFn = vi.fn().mockResolvedValue(undefined);
      const result = await transactionalOutbox.processPendingEvents(processFn);

      expect(result).toEqual({ processed: 1, failed: 0 });
      expect(processFn).toHaveBeenCalledWith(
        expect.objectContaining({
          eventId: 'integration-event-123',
          eventType: 'appointment.created',
          aggregateId: mockAppointment.id,
        })
      );

      // Verify event was marked as processed
      expect(mockPrismaClient.$executeRawUnsafe).toHaveBeenCalledWith(
        expect.stringContaining('UPDATE outbox_events'),
        expect.any(Date),
        'outbox-integration-1'
      );
    });

    it('should handle outbox processing failures with retry logic', async () => {
      const mockEvent = {
        eventId: 'failing-event-123',
        eventType: 'appointment.created',
        aggregateId: 'failing-appointment',
        aggregateType: 'appointment',
        data: mockAppointment,
        timestamp: new Date().toISOString(),
        eventVersion: 1,
        source: 'test-source',
        metadata: {},
      };

      // Mock pending events with one that will fail
      vi.mocked(mockPrismaClient.$queryRawUnsafe).mockResolvedValue([
        {
          id: 'outbox-failing-1',
          payload: JSON.stringify(mockEvent),
          retryCount: 0,
        },
      ]);

      // Process with failing function
      const processFn = vi
        .fn()
        .mockRejectedValue(new Error('Processing failed'));
      const result = await transactionalOutbox.processPendingEvents(
        processFn,
        10,
        3
      );

      expect(result).toEqual({ processed: 0, failed: 1 });

      // Verify retry count was incremented
      expect(mockPrismaClient.$executeRawUnsafe).toHaveBeenCalledWith(
        expect.stringContaining('UPDATE outbox_events'),
        1, // retry_count + 1
        'Processing failed',
        3, // maxRetries
        expect.any(Date),
        'outbox-failing-1'
      );
    });
  });

  describe('Error Handling', () => {
    it('should handle connection failures gracefully', async () => {
      // Mock connection failure
      const connectionError = new Error('NATS connection failed');
      const mockEventPublisher = {
        publish: vi.fn().mockRejectedValue(connectionError),
      } as any;

      const testAppointmentEventPublisher = new AppointmentEventPublisher(
        mockEventPublisher
      );

      await expect(
        testAppointmentEventPublisher.publishAppointmentCreated(
          mockAppointment,
          mockEventOptions
        )
      ).rejects.toThrow('NATS connection failed');
    });

    it('should handle database failures in outbox operations', async () => {
      const dbError = new Error('Database connection lost');
      vi.mocked(mockPrismaClient.$executeRawUnsafe).mockRejectedValue(dbError);

      const mockEvent = {
        eventId: 'db-error-event',
        eventType: 'appointment.created',
        aggregateId: 'test-appointment',
        aggregateType: 'appointment',
        data: mockAppointment,
        timestamp: new Date().toISOString(),
        eventVersion: 1,
        source: 'test-source',
        metadata: {},
      };

      await expect(
        transactionalOutbox.add(mockEvent as any, mockPrismaClient)
      ).rejects.toThrow('Database connection lost');
    });
  });
});
