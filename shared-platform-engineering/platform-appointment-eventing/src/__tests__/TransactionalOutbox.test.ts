import { beforeEach, describe, expect, it, vi } from 'vitest';
import type { Prisma, PrismaClient } from '@prisma/client';
import { UnifiedAppointmentStatus } from '@beauty-crm/product-appointment-types';
import { PrismaTransactionalOutbox } from '../outbox/TransactionalOutbox';
import type { AppointmentEvent } from '../events';

// Mock Prisma Client
interface MockPrismaClient extends PrismaClient {
  $executeRawUnsafe: ReturnType<typeof vi.fn>;
  $queryRawUnsafe: ReturnType<typeof vi.fn>;
  $transaction: ReturnType<typeof vi.fn>;
}

const mockPrismaClient: MockPrismaClient = {
  $executeRawUnsafe: vi.fn(),
  $queryRawUnsafe: vi.fn(),
  $transaction: vi.fn(),
} as any;

const mockTransactionClient: Prisma.TransactionClient = {
  $executeRawUnsafe: vi.fn(),
  $queryRawUnsafe: vi.fn(),
} as any;

// Mock appointment event for testing
const mockAppointmentEvent: AppointmentEvent = {
  eventId: 'event-123',
  eventType: 'appointment.created',
  aggregateId: 'appointment-456',
  aggregateType: 'appointment',
  data: {
    appointment: {
      id: 'appointment-456',
      salonId: 'salon-789',
      customerId: 'customer-101',
      customerName: 'John Doe',
      customerEmail: '<EMAIL>',
      treatmentId: 'treatment-202',
      treatmentName: 'Haircut',
      treatmentDuration: 60,
      treatmentPrice: 50,
      staffId: 'staff-303',
      startTime: new Date('2024-01-15T10:00:00Z'),
      endTime: new Date('2024-01-15T11:00:00Z'),
      status: UnifiedAppointmentStatus.CONFIRMED,
      source: 'PLANNER',
      createdAt: new Date('2024-01-01T00:00:00Z'),
      updatedAt: new Date('2024-01-01T00:00:00Z'),
    },
  },
  timestamp: '2024-01-01T00:00:00.000Z',
  eventVersion: 1,
  source: 'appointment-service',
};

describe('PrismaTransactionalOutbox', () => {
  let outbox: PrismaTransactionalOutbox;

  beforeEach(() => {
    outbox = new PrismaTransactionalOutbox(mockPrismaClient);
    vi.clearAllMocks();
  });

  describe('constructor', () => {
    it('should use default outbox table name', () => {
      const defaultOutbox = new PrismaTransactionalOutbox(mockPrismaClient);
      expect(defaultOutbox).toBeInstanceOf(PrismaTransactionalOutbox);
    });

    it('should accept custom outbox table name', () => {
      const customOutbox = new PrismaTransactionalOutbox(
        mockPrismaClient,
        'custom_outbox',
      );
      expect(customOutbox).toBeInstanceOf(PrismaTransactionalOutbox);
    });
  });

  describe('add', () => {
    it('should add event to outbox using transaction client', async () => {
      await outbox.add(mockAppointmentEvent, mockTransactionClient);

      expect(mockTransactionClient.$executeRawUnsafe).toHaveBeenCalledWith(
        expect.stringContaining('INSERT INTO outbox_events'),
        expect.any(String), // UUID
        mockAppointmentEvent.aggregateId,
        mockAppointmentEvent.eventType,
        JSON.stringify(mockAppointmentEvent),
        expect.any(Date),
        0, // retry_count
        'PENDING',
      );
    });

    it('should add event to outbox using direct prisma client when no transaction', async () => {
      await outbox.add(mockAppointmentEvent, null as any);

      expect(mockPrismaClient.$executeRawUnsafe).toHaveBeenCalledWith(
        expect.stringContaining('INSERT INTO outbox_events'),
        expect.any(String), // UUID
        mockAppointmentEvent.aggregateId,
        mockAppointmentEvent.eventType,
        JSON.stringify(mockAppointmentEvent),
        expect.any(Date),
        0, // retry_count
        'PENDING',
      );
    });

    it('should use custom table name in SQL', async () => {
      const customOutbox = new PrismaTransactionalOutbox(
        mockPrismaClient,
        'custom_events',
      );

      await customOutbox.add(mockAppointmentEvent, mockTransactionClient);

      expect(mockTransactionClient.$executeRawUnsafe).toHaveBeenCalledWith(
        expect.stringContaining('INSERT INTO custom_events'),
        expect.any(String),
        expect.any(String),
        expect.any(String),
        expect.any(String),
        expect.any(Date),
        expect.any(Number),
        expect.any(String),
      );
    });

    it('should handle database errors', async () => {
      const dbError = new Error('Database connection failed');
      vi.mocked(mockTransactionClient.$executeRawUnsafe).mockRejectedValue(
        dbError,
      );

      await expect(
        outbox.add(mockAppointmentEvent, mockTransactionClient),
      ).rejects.toThrow('Database connection failed');
    });
  });

  describe('processPendingEvents', () => {
    const mockOutboxEvents = [
      {
        id: 'outbox-1',
        payload: JSON.stringify(mockAppointmentEvent),
        retryCount: 0,
      },
      {
        id: 'outbox-2',
        payload: JSON.stringify({
          ...mockAppointmentEvent,
          eventId: 'event-456',
          aggregateId: 'appointment-789',
        }),
        retryCount: 1,
      },
    ];

    beforeEach(() => {
      // Mock transaction to call the callback with mock transaction client
      vi.mocked(mockPrismaClient.$transaction).mockImplementation(
        async (callback: any) => {
          return callback(mockTransactionClient);
        },
      );
    });

    it('should process pending events successfully', async () => {
      vi.mocked(mockTransactionClient.$queryRawUnsafe).mockResolvedValue(
        mockOutboxEvents,
      );
      vi.mocked(mockTransactionClient.$executeRawUnsafe).mockResolvedValue(
        undefined,
      );

      const processFn = vi.fn().mockResolvedValue(undefined);

      const result = await outbox.processPendingEvents(processFn);

      expect(result).toEqual({ processed: 2, failed: 0 });
      expect(processFn).toHaveBeenCalledTimes(2);

      // Check that the first call was made with the parsed event
      expect(processFn).toHaveBeenNthCalledWith(
        1,
        expect.objectContaining({
          eventId: 'event-123',
          eventType: 'appointment.created',
          aggregateId: 'appointment-456',
        }),
      );

      // Verify events were marked as processed
      expect(mockTransactionClient.$executeRawUnsafe).toHaveBeenCalledWith(
        expect.stringContaining('UPDATE outbox_events'),
        expect.any(Date),
        'outbox-1',
      );
    });

    it('should handle processing failures and update retry count', async () => {
      vi.mocked(mockTransactionClient.$queryRawUnsafe).mockResolvedValue([
        mockOutboxEvents[0],
      ]);
      vi.mocked(mockTransactionClient.$executeRawUnsafe).mockResolvedValue(
        undefined,
      );

      const processFn = vi
        .fn()
        .mockRejectedValue(new Error('Processing failed'));

      const result = await outbox.processPendingEvents(processFn);

      expect(result).toEqual({ processed: 0, failed: 1 });

      // Verify retry count was updated
      expect(mockTransactionClient.$executeRawUnsafe).toHaveBeenCalledWith(
        expect.stringContaining('UPDATE outbox_events'),
        1, // retry_count + 1
        'Processing failed',
        3, // maxRetries
        expect.any(Date),
        'outbox-1',
      );
    });

    it('should use custom batch size and max retries', async () => {
      vi.mocked(mockTransactionClient.$queryRawUnsafe).mockResolvedValue([]);

      const processFn = vi.fn();
      await outbox.processPendingEvents(processFn, 25, 5);

      expect(mockTransactionClient.$queryRawUnsafe).toHaveBeenCalledWith(
        expect.stringContaining('LIMIT $2'),
        5, // maxRetries
        25, // batchSize
      );
    });

    it('should handle empty result set', async () => {
      vi.mocked(mockTransactionClient.$queryRawUnsafe).mockResolvedValue([]);

      const processFn = vi.fn();
      const result = await outbox.processPendingEvents(processFn);

      expect(result).toEqual({ processed: 0, failed: 0 });
      expect(processFn).not.toHaveBeenCalled();
    });

    it('should handle malformed JSON in payload', async () => {
      const malformedEvent = {
        id: 'outbox-bad',
        payload: 'invalid-json',
        retryCount: 0,
      };

      vi.mocked(mockTransactionClient.$queryRawUnsafe).mockResolvedValue([
        malformedEvent,
      ]);
      vi.mocked(mockTransactionClient.$executeRawUnsafe).mockResolvedValue(
        undefined,
      );

      const processFn = vi.fn();
      const result = await outbox.processPendingEvents(processFn);

      expect(result).toEqual({ processed: 0, failed: 1 });
      expect(processFn).not.toHaveBeenCalled();

      // Verify error was logged and retry count updated
      expect(mockTransactionClient.$executeRawUnsafe).toHaveBeenCalledWith(
        expect.stringContaining('UPDATE outbox_events'),
        1, // retry_count + 1
        expect.stringContaining('Unexpected token'), // JSON parse error
        3, // maxRetries
        expect.any(Date),
        'outbox-bad',
      );
    });

    it('should use custom table name in queries', async () => {
      const customOutbox = new PrismaTransactionalOutbox(
        mockPrismaClient,
        'custom_events',
      );

      vi.mocked(mockTransactionClient.$queryRawUnsafe).mockResolvedValue([]);

      const processFn = vi.fn();
      await customOutbox.processPendingEvents(processFn);

      expect(mockTransactionClient.$queryRawUnsafe).toHaveBeenCalledWith(
        expect.stringContaining('FROM custom_events'),
        expect.any(Number),
        expect.any(Number),
      );
    });

    it('should handle transaction errors', async () => {
      const transactionError = new Error('Transaction failed');
      vi.mocked(mockPrismaClient.$transaction).mockRejectedValue(
        transactionError,
      );

      const processFn = vi.fn();

      await expect(outbox.processPendingEvents(processFn)).rejects.toThrow(
        'Transaction failed',
      );
    });
  });
});
