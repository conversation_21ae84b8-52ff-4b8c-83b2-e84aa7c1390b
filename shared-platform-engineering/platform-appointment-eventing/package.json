{"author": "", "dependencies": {"@beauty-crm/platform-eventing": "workspace:*", "@beauty-crm/product-appointment-types": "workspace:*"}, "description": "Shared eventing infrastructure for appointment-related events across the platform.", "devDependencies": {"@types/uuid": "^10.0.0", "rimraf": "^6.0.1", "typescript": "^5.8.3", "uuid": "^11.0.4", "vitest": "^3.2.4"}, "keywords": ["events", "appointments", "nats", "outbox"], "license": "ISC", "main": "dist/index.js", "name": "@beauty-crm/platform-appointment-eventing", "peerDependencies": {"@prisma/client": "^6.11.0"}, "scripts": {"build": "tsc", "clean": "<PERSON><PERSON><PERSON> dist", "dev": "tsc --watch", "format": "biome format --write --fix .", "lint": "biome lint .", "lint:check": "biome check .", "lint:fix": "biome lint --write .", "test": "vitest", "test:run": "vitest run", "test:coverage": "vitest run --coverage"}, "types": "dist/index.d.ts", "version": "1.0.0"}