# 🚀 Migration Guide: Eliminate Schema Duplication

This guide shows you how to migrate your existing Beauty CRM appointment services to use the **Single Source of Truth** schema library.

## 📋 Pre-Migration Checklist

- [ ] Backup your current codebase
- [ ] Install the schema package: `bun add @beauty-crm/platform-appointment-schema`
- [ ] Update your Prisma schema
- [ ] Run `prisma db push` to sync database

## 🔄 Step-by-Step Migration

### Step 1: Update Package Dependencies

```json
// In your service's package.json
{
  "dependencies": {
    "@beauty-crm/platform-appointment-schema": "workspace:*",
    // ... other dependencies
  }
}
```

### Step 2: Replace Domain Models

**Before (appointment-planner-backend/src/domain/models/appointment.ts):**
```typescript
// ❌ DELETE THIS FILE - 150+ lines of duplicate code
export interface IAppointment {
  id?: string;
  salonId: string;
  customerName: string;
  // ... 30+ duplicate fields
}

export class Appointment implements IAppointment {
  // ... 100+ lines of duplicate implementation
}
```

**After:**
```typescript
// ✅ USE THIS INSTEAD - Import from schema library
import {
  Appointment,
  AppointmentDomainModel,
  APPOINTMENT_STATUSES,
} from '@beauty-crm/platform-appointment-schema';

// Focus on business logic, not data structure
export class AppointmentDomain implements AppointmentDomainModel {
  // All properties auto-derived from schema
  // Focus on business methods like confirm(), cancel(), etc.
}
```

### Step 3: Replace Repository Mapping

**Before (manual Prisma mapping):**
```typescript
// ❌ DELETE 50+ lines of manual mapping
const toDomain = (prisma: PrismaAppointment): Appointment => {
  return {
    id: prisma.id,
    salonId: prisma.salonId,
    customerName: prisma.customerName,
    // ... 30+ lines of manual field mapping
  };
};

const toPrisma = (appointment: Appointment) => {
  return {
    id: appointment.id,
    salon_id: appointment.salonId,
    customer_name: appointment.customerName,
    // ... 30+ lines of manual field mapping
  };
};
```

**After:**
```typescript
// ✅ USE SCHEMA LIBRARY ADAPTERS - Zero manual mapping!
import {
  prismaToAppointment,
  appointmentToPrismaCreate,
  appointmentToPrismaUpdate,
} from '@beauty-crm/platform-appointment-schema';

class AppointmentRepository {
  async findById(id: string): Promise<Appointment | null> {
    const prismaAppointment = await this.prisma.appointment.findUnique({ where: { id } });
    return prismaAppointment ? prismaToAppointment(prismaAppointment) : null;
  }
  
  async create(request: CreateAppointmentRequest): Promise<Appointment> {
    const prismaData = appointmentToPrismaCreate(request);
    const created = await this.prisma.appointment.create({ data: prismaData });
    return prismaToAppointment(created);
  }
}
```

### Step 4: Replace Controller Validation

**Before (duplicate Zod schemas):**
```typescript
// ❌ DELETE 50+ lines of duplicate validation
const createAppointmentSchema = z.object({
  customerEmail: z.string().email('Valid email is required'),
  customerName: z.string().min(1, 'Name is required'),
  customerPhone: z.string().optional(),
  // ... 20+ duplicate field validations
});

const updateAppointmentSchema = z.object({
  // ... another 20+ duplicate field validations
});
```

**After:**
```typescript
// ✅ USE SCHEMA LIBRARY VALIDATION - Single source of truth!
import {
  CreateAppointmentSchema,
  UpdateAppointmentSchema,
  validateCreateRequest,
  validateUpdateRequest,
} from '@beauty-crm/platform-appointment-schema';

export class AppointmentController {
  createAppointmentValidator = zValidator('json', CreateAppointmentSchema);
  updateAppointmentValidator = zValidator('json', UpdateAppointmentSchema);
  
  createAppointment = async (c: Context) => {
    const validatedData = validateCreateRequest(c.req.valid('json'));
    // ... rest of logic
  };
}
```

### Step 5: Replace Event Schemas

**Before (duplicate event definitions):**
```typescript
// ❌ DELETE duplicate event schemas
export const AppointmentEventSchema = z.object({
  eventId: z.string(),
  eventType: z.enum(['appointment.created', 'appointment.updated']),
  // ... 20+ duplicate event fields
});
```

**After:**
```typescript
// ✅ USE SCHEMA LIBRARY EVENTS
import {
  createAppointmentCreatedEvent,
  createAppointmentUpdatedEvent,
  AppointmentCreatedEvent,
} from '@beauty-crm/platform-appointment-schema';

// Create events with single function call
const event = createAppointmentCreatedEvent(appointment, {
  source: 'appointment-planner-backend',
  correlationId: 'req-123',
});
```

### Step 6: Replace Constants and Enums

**Before (scattered magic strings):**
```typescript
// ❌ DELETE scattered constants
export enum AppointmentStatus {
  PENDING = 'PENDING',
  CONFIRMED = 'CONFIRMED',
  // ... duplicate in every service
}
```

**After:**
```typescript
// ✅ USE SCHEMA LIBRARY CONSTANTS
import {
  APPOINTMENT_STATUSES,
  APPOINTMENT_EVENT_TYPES,
  APPOINTMENT_SOURCES,
} from '@beauty-crm/platform-appointment-schema';

// Use consistent constants everywhere
if (appointment.status === APPOINTMENT_STATUSES.PENDING) {
  // ...
}
```

## 🗂️ Files to Delete After Migration

Once you've migrated to the schema library, you can **delete these duplicate files**:

### Appointment Planner Backend
- [ ] `src/domain/models/appointment.ts` (150+ lines)
- [ ] `src/shared/types/appointment.ts` (50+ lines)
- [ ] `src/presentation/validators/appointment.ts` (100+ lines)

### Appointment Management Backend  
- [ ] `src/shared/types/appointment-events.ts` (200+ lines)
- [ ] `src/domain/entities/appointment.ts` (100+ lines)
- [ ] `src/infrastructure/mappers/appointment-mapper.ts` (80+ lines)

### Platform Libraries
- [ ] `shared-product-engineering/product-appointment-types/src/appointment/types.ts` (300+ lines)
- [ ] `shared-product-engineering/product-appointment-types/src/adapters/AppointmentTypeAdapter.ts` (280+ lines)

**Total lines deleted: ~1,260+ lines of duplicate code!** 🔥

## ✅ Migration Verification

After migration, verify everything works:

```bash
# 1. Build all services
bun run build

# 2. Run tests
bun run test

# 3. Check TypeScript compilation
tsc --noEmit

# 4. Verify API endpoints still work
curl -X POST http://localhost:3000/appointments \
  -H "Content-Type: application/json" \
  -d '{"customerName": "Test", "customerEmail": "<EMAIL>", ...}'
```

## 🎯 Benefits After Migration

- **90% Less Code**: Eliminated 1,260+ lines of duplicate schemas
- **Single Source of Truth**: One schema definition for all services
- **Type Safety**: Consistent types across all layers
- **Auto-Validation**: Zod schemas generated from core schema
- **Auto-Mapping**: Prisma adapters handle all conversions
- **Maintainability**: Change schema once, update everywhere
- **Consistency**: No more field name mismatches between services

## 🚨 Common Migration Issues

### Issue 1: Optional vs Required Fields
**Problem**: TypeScript errors about optional fields
**Solution**: Update your domain interface to match the schema library types

### Issue 2: Field Name Mismatches  
**Problem**: Database field names don't match schema
**Solution**: Update your Prisma schema to match the generated schema

### Issue 3: Event Structure Changes
**Problem**: Existing events don't match new event schemas
**Solution**: Use the migration helpers in the schema library

## 🎉 You're Done!

Your appointment services now use a **Single Source of Truth** for all schemas. No more duplication, no more inconsistencies, no more maintenance headaches!

**Welcome to the real daddy way of handling DDD schemas!** 🔥
