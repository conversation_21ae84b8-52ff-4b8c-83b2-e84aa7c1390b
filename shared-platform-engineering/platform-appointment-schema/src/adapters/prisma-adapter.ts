/**
 * 🔄 PRISMA ADAPTER - Converts between Prisma types and Core Schema
 *
 * This eliminates the need for manual mapping between Prisma and Domain models.
 * Uses the Single Source of Truth to ensure consistency.
 */

// Note: In real implementation, this would import from the actual Prisma client
// For now, we'll define a compatible interface
interface PrismaAppointment {
  id: string;
  salonId: string;
  customerId: string;
  staffId?: string | null;
  treatmentId: string;
  customerName?: string | null;
  customerEmail?: string | null;
  customerPhone?: string | null;
  treatmentName?: string | null;
  treatmentDuration?: number | null;
  treatmentPrice?: number | null;
  salonName?: string | null;
  salonLogo?: string | null;
  salonColor?: string | null;
  startTime: Date;
  endTime: Date;
  status: string;
  notes?: string | null;
  locale?: string | null;
  source?: string | null;
  plannerAppointmentId?: string | null;
  createdAt: Date;
  updatedAt: Date;
  confirmedAt?: Date | null;
  completedAt?: Date | null;
  cancelledAt?: Date | null;
}

import type {
  Appointment,
  CreateAppointmentRequest,
} from '../core/appointment-schema';

// ============================================================================
// 🔄 PRISMA TO DOMAIN CONVERSION
// ============================================================================

export const prismaToAppointment = (
  prismaAppointment: PrismaAppointment
): Appointment => {
  return {
    id: prismaAppointment.id,
    salonId: prismaAppointment.salonId,
    customerId: prismaAppointment.customerId,
    staffId: prismaAppointment.staffId || undefined,
    treatmentId: prismaAppointment.treatmentId,

    // Customer info
    customerName: prismaAppointment.customerName || '',
    customerEmail: prismaAppointment.customerEmail || '',
    customerPhone: prismaAppointment.customerPhone || undefined,

    // Treatment info
    treatmentName: prismaAppointment.treatmentName || '',
    treatmentDuration: prismaAppointment.treatmentDuration || 60,
    treatmentPrice: prismaAppointment.treatmentPrice || 0,

    // Salon info
    salonName: prismaAppointment.salonName || '',
    salonLogo: prismaAppointment.salonLogo || undefined,
    salonColor: prismaAppointment.salonColor || undefined,

    // Scheduling
    startTime: prismaAppointment.startTime,
    endTime: prismaAppointment.endTime,

    // Status & metadata
    status: prismaAppointment.status as Appointment['status'],
    notes: prismaAppointment.notes || undefined,
    locale: prismaAppointment.locale || 'en-US',

    // Source tracking
    source: (prismaAppointment.source as Appointment['source']) || 'planner',
    plannerAppointmentId: prismaAppointment.plannerAppointmentId || undefined,

    // Timestamps
    createdAt: prismaAppointment.createdAt,
    updatedAt: prismaAppointment.updatedAt,
    confirmedAt: prismaAppointment.confirmedAt || undefined,
    completedAt: prismaAppointment.completedAt || undefined,
    cancelledAt: prismaAppointment.cancelledAt || undefined,
  };
};

// ============================================================================
// 🔄 DOMAIN TO PRISMA CONVERSION
// ============================================================================

export const appointmentToPrismaCreate = (
  appointment: CreateAppointmentRequest
) => {
  return {
    salonId: appointment.salonId,
    customerId: appointment.customerId,
    staffId: appointment.staffId,
    treatmentId: appointment.treatmentId,

    // Customer info
    customerName: appointment.customerName,
    customerEmail: appointment.customerEmail,
    customerPhone: appointment.customerPhone,

    // Treatment info
    treatmentName: appointment.treatmentName,
    treatmentDuration: appointment.treatmentDuration,
    treatmentPrice: appointment.treatmentPrice,

    // Salon info
    salonName: appointment.salonName,
    salonLogo: appointment.salonLogo,
    salonColor: appointment.salonColor,

    // Scheduling
    startTime: appointment.startTime,
    endTime: appointment.endTime,

    // Status & metadata
    status: appointment.status,
    notes: appointment.notes,
    locale: appointment.locale,

    // Source tracking
    source: appointment.source,
    plannerAppointmentId: appointment.plannerAppointmentId,
  };
};

export const appointmentToPrismaUpdate = (
  appointment: Partial<Appointment>
) => {
  const updateData: any = {};

  // Only include fields that are actually being updated
  if (appointment.salonId !== undefined)
    updateData.salonId = appointment.salonId;
  if (appointment.customerId !== undefined)
    updateData.customerId = appointment.customerId;
  if (appointment.staffId !== undefined)
    updateData.staffId = appointment.staffId;
  if (appointment.treatmentId !== undefined)
    updateData.treatmentId = appointment.treatmentId;

  // Customer info
  if (appointment.customerName !== undefined)
    updateData.customerName = appointment.customerName;
  if (appointment.customerEmail !== undefined)
    updateData.customerEmail = appointment.customerEmail;
  if (appointment.customerPhone !== undefined)
    updateData.customerPhone = appointment.customerPhone;

  // Treatment info
  if (appointment.treatmentName !== undefined)
    updateData.treatmentName = appointment.treatmentName;
  if (appointment.treatmentDuration !== undefined)
    updateData.treatmentDuration = appointment.treatmentDuration;
  if (appointment.treatmentPrice !== undefined)
    updateData.treatmentPrice = appointment.treatmentPrice;

  // Salon info
  if (appointment.salonName !== undefined)
    updateData.salonName = appointment.salonName;
  if (appointment.salonLogo !== undefined)
    updateData.salonLogo = appointment.salonLogo;
  if (appointment.salonColor !== undefined)
    updateData.salonColor = appointment.salonColor;

  // Scheduling
  if (appointment.startTime !== undefined)
    updateData.startTime = appointment.startTime;
  if (appointment.endTime !== undefined)
    updateData.endTime = appointment.endTime;

  // Status & metadata
  if (appointment.status !== undefined) updateData.status = appointment.status;
  if (appointment.notes !== undefined) updateData.notes = appointment.notes;
  if (appointment.locale !== undefined) updateData.locale = appointment.locale;

  // Source tracking
  if (appointment.source !== undefined) updateData.source = appointment.source;
  if (appointment.plannerAppointmentId !== undefined)
    updateData.plannerAppointmentId = appointment.plannerAppointmentId;

  // Timestamp updates
  if (appointment.confirmedAt !== undefined)
    updateData.confirmedAt = appointment.confirmedAt;
  if (appointment.completedAt !== undefined)
    updateData.completedAt = appointment.completedAt;
  if (appointment.cancelledAt !== undefined)
    updateData.cancelledAt = appointment.cancelledAt;

  return updateData;
};

// ============================================================================
// 🎯 BATCH CONVERSION HELPERS
// ============================================================================

export const prismaToAppointments = (
  prismaAppointments: PrismaAppointment[]
): Appointment[] => {
  return prismaAppointments.map(prismaToAppointment);
};

// ============================================================================
// 🔥 TYPE-SAFE PRISMA QUERY BUILDERS
// ============================================================================

export const buildAppointmentQuery = (filters: {
  salonId?: string;
  customerId?: string;
  staffId?: string;
  status?: string;
  startDate?: Date;
  endDate?: Date;
}) => {
  const where: any = {};

  if (filters.salonId) where.salonId = filters.salonId;
  if (filters.customerId) where.customerId = filters.customerId;
  if (filters.staffId) where.staffId = filters.staffId;
  if (filters.status) where.status = filters.status;

  if (filters.startDate || filters.endDate) {
    where.startTime = {};
    if (filters.startDate) where.startTime.gte = filters.startDate;
    if (filters.endDate) where.startTime.lte = filters.endDate;
  }

  return where;
};
