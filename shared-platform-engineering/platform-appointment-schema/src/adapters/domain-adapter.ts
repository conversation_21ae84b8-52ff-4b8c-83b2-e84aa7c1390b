/**
 * 🏗️ DOMAIN ADAPTER - Converts between Core Schema and Domain Models
 *
 * This eliminates duplication between your domain models and the core schema.
 * Your domain classes can focus on business logic while this handles conversion.
 */

import type {
  Appointment,
  CreateAppointmentRequest,
  AppointmentStatus,
} from '../core/appointment-schema';

// ============================================================================
// 🏗️ DOMAIN MODEL INTERFACE (What your domain classes should implement)
// ============================================================================

export interface AppointmentDomainModel {
  readonly id: string;
  readonly salonId: string;
  readonly customerId: string;
  readonly staffId?: string | undefined;
  readonly treatmentId: string;

  // Customer info
  readonly customerName: string;
  readonly customerEmail: string;
  readonly customerPhone?: string | undefined;

  // Treatment info
  readonly treatmentName: string;
  readonly treatmentDuration: number;
  readonly treatmentPrice: number;

  // Salon info
  readonly salonName: string;
  readonly salonLogo?: string | undefined;
  readonly salonColor?: string | undefined;

  // Scheduling
  readonly startTime: Date;
  readonly endTime: Date;

  // Status & metadata
  readonly status: AppointmentStatus;
  readonly notes?: string | undefined;
  readonly locale: string;

  // Source tracking
  readonly source: 'planner' | 'management' | 'api' | 'import';
  readonly plannerAppointmentId?: string | undefined;

  // Timestamps
  readonly createdAt: Date;
  readonly updatedAt: Date;
  readonly confirmedAt?: Date | undefined;
  readonly completedAt?: Date | undefined;
  readonly cancelledAt?: Date | undefined;

  // Domain methods
  confirm(): void;
  cancel(reason?: string): void;
  complete(): void;
  reschedule(newStartTime: Date, newEndTime: Date): void;
  isOverlapping(other: AppointmentDomainModel): boolean;
  canBeModified(): boolean;
  getDuration(): number;
  getTimeSlot(): { start: Date; end: Date };
}

// ============================================================================
// 🔄 DOMAIN TO CORE CONVERSION
// ============================================================================

export const domainToAppointment = (
  domain: AppointmentDomainModel
): Appointment => {
  return {
    id: domain.id,
    salonId: domain.salonId,
    customerId: domain.customerId,
    staffId: domain.staffId,
    treatmentId: domain.treatmentId,

    // Customer info
    customerName: domain.customerName,
    customerEmail: domain.customerEmail,
    customerPhone: domain.customerPhone,

    // Treatment info
    treatmentName: domain.treatmentName,
    treatmentDuration: domain.treatmentDuration,
    treatmentPrice: domain.treatmentPrice,

    // Salon info
    salonName: domain.salonName,
    salonLogo: domain.salonLogo,
    salonColor: domain.salonColor,

    // Scheduling
    startTime: domain.startTime,
    endTime: domain.endTime,

    // Status & metadata
    status: domain.status,
    notes: domain.notes,
    locale: domain.locale,

    // Source tracking
    source: domain.source,
    plannerAppointmentId: domain.plannerAppointmentId,

    // Timestamps
    createdAt: domain.createdAt,
    updatedAt: domain.updatedAt,
    confirmedAt: domain.confirmedAt,
    completedAt: domain.completedAt,
    cancelledAt: domain.cancelledAt,
  };
};

// ============================================================================
// 🔄 CORE TO DOMAIN CONVERSION
// ============================================================================

export const appointmentToDomain = (
  appointment: Appointment
): AppointmentDomainModel => {
  return {
    id: appointment.id,
    salonId: appointment.salonId,
    customerId: appointment.customerId,
    staffId: appointment.staffId,
    treatmentId: appointment.treatmentId,

    // Customer info
    customerName: appointment.customerName,
    customerEmail: appointment.customerEmail,
    customerPhone: appointment.customerPhone,

    // Treatment info
    treatmentName: appointment.treatmentName,
    treatmentDuration: appointment.treatmentDuration,
    treatmentPrice: appointment.treatmentPrice,

    // Salon info
    salonName: appointment.salonName,
    salonLogo: appointment.salonLogo,
    salonColor: appointment.salonColor,

    // Scheduling
    startTime: appointment.startTime,
    endTime: appointment.endTime,

    // Status & metadata
    status: appointment.status,
    notes: appointment.notes,
    locale: appointment.locale,

    // Source tracking
    source: appointment.source,
    plannerAppointmentId: appointment.plannerAppointmentId,

    // Timestamps
    createdAt: appointment.createdAt,
    updatedAt: appointment.updatedAt,
    confirmedAt: appointment.confirmedAt,
    completedAt: appointment.completedAt,
    cancelledAt: appointment.cancelledAt,

    // Domain methods implementation
    confirm() {
      // This would be implemented in your actual domain class
      throw new Error('Domain method not implemented in adapter');
    },

    cancel(reason?: string) {
      throw new Error('Domain method not implemented in adapter');
    },

    complete() {
      throw new Error('Domain method not implemented in adapter');
    },

    reschedule(newStartTime: Date, newEndTime: Date) {
      throw new Error('Domain method not implemented in adapter');
    },

    isOverlapping(other: AppointmentDomainModel): boolean {
      return (
        this.startTime < other.endTime &&
        this.endTime > other.startTime &&
        this.salonId === other.salonId &&
        this.staffId === other.staffId
      );
    },

    canBeModified(): boolean {
      return !['COMPLETED', 'CANCELLED'].includes(this.status);
    },

    getDuration(): number {
      return this.treatmentDuration;
    },

    getTimeSlot(): { start: Date; end: Date } {
      return {
        start: this.startTime,
        end: this.endTime,
      };
    },
  };
};

// ============================================================================
// 🎯 BATCH CONVERSION HELPERS
// ============================================================================

export const domainToAppointments = (
  domains: AppointmentDomainModel[]
): Appointment[] => {
  return domains.map(domainToAppointment);
};

export const appointmentsToDomain = (
  appointments: Appointment[]
): AppointmentDomainModel[] => {
  return appointments.map(appointmentToDomain);
};

// ============================================================================
// 🔥 DOMAIN FACTORY
// ============================================================================

export const createAppointmentDomain = (
  data: CreateAppointmentRequest
): Omit<AppointmentDomainModel, 'id' | 'createdAt' | 'updatedAt'> => {
  return {
    salonId: data.salonId,
    customerId: data.customerId,
    staffId: data.staffId,
    treatmentId: data.treatmentId,

    // Customer info
    customerName: data.customerName,
    customerEmail: data.customerEmail,
    customerPhone: data.customerPhone,

    // Treatment info
    treatmentName: data.treatmentName,
    treatmentDuration: data.treatmentDuration,
    treatmentPrice: data.treatmentPrice,

    // Salon info
    salonName: data.salonName,
    salonLogo: data.salonLogo,
    salonColor: data.salonColor,

    // Scheduling
    startTime: data.startTime,
    endTime: data.endTime,

    // Status & metadata
    status: data.status,
    notes: data.notes,
    locale: data.locale,

    // Source tracking
    source: data.source,
    plannerAppointmentId: data.plannerAppointmentId,

    // Timestamps (optional, will be set by domain)
    confirmedAt: undefined,
    completedAt: undefined,
    cancelledAt: undefined,

    // Domain methods (placeholders)
    confirm: () => {
      throw new Error('Not implemented');
    },
    cancel: () => {
      throw new Error('Not implemented');
    },
    complete: () => {
      throw new Error('Not implemented');
    },
    reschedule: () => {
      throw new Error('Not implemented');
    },
    isOverlapping: () => false,
    canBeModified: () => true,
    getDuration: () => data.treatmentDuration,
    getTimeSlot: () => ({ start: data.startTime, end: data.endTime }),
  };
};
