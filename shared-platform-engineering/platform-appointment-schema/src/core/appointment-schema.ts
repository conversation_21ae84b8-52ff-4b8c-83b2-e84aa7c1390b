/**
 * 🎯 SINGLE SOURCE OF TRUTH for Appointment Schema
 * 
 * This is the ONLY place where appointment structure is defined.
 * All other layers (Prisma, Domain, API, Events) derive from this.
 */

import { z } from 'zod';

// ============================================================================
// 🔥 CORE APPOINTMENT SCHEMA - The One True Definition
// ============================================================================

export const AppointmentStatusSchema = z.enum([
  'PENDING',
  'CONFIRMED', 
  'SCHEDULED',
  'IN_PROGRESS',
  'COMPLETED',
  'CANCELLED',
  'NO_SHOW',
  'RESCHEDULED'
]);

export const AppointmentCoreSchema = z.object({
  // Identity
  id: z.string().cuid(),
  salonId: z.string().cuid(),
  customerId: z.string().cuid(),
  staffId: z.string().cuid().optional(),
  treatmentId: z.string().cuid(),
  
  // Customer Info (denormalized for performance)
  customerName: z.string().min(1),
  customerEmail: z.string().email(),
  customerPhone: z.string().optional(),
  
  // Treatment Info (denormalized for performance)  
  treatmentName: z.string().min(1),
  treatmentDuration: z.number().int().positive(),
  treatmentPrice: z.number().positive(),
  
  // Salon Info (denormalized for performance)
  salonName: z.string().min(1),
  salonLogo: z.string().url().optional(),
  salonColor: z.string().optional(),
  
  // Scheduling
  startTime: z.date(),
  endTime: z.date(),
  
  // Status & Metadata
  status: AppointmentStatusSchema,
  notes: z.string().optional(),
  locale: z.string().default('en-US'),
  
  // Source tracking
  source: z.enum(['planner', 'management', 'api', 'import']).default('planner'),
  plannerAppointmentId: z.string().optional(), // Cross-service reference
  
  // Timestamps
  createdAt: z.date(),
  updatedAt: z.date(),
  confirmedAt: z.date().optional(),
  completedAt: z.date().optional(),
  cancelledAt: z.date().optional(),
});

// ============================================================================
// 🎯 DERIVED SCHEMAS - Auto-generated from Core
// ============================================================================

// For API Creation (no ID, timestamps auto-generated)
export const CreateAppointmentSchema = AppointmentCoreSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
  confirmedAt: true,
  completedAt: true,
  cancelledAt: true,
});

// For API Updates (all fields optional except ID)
export const UpdateAppointmentSchema = AppointmentCoreSchema.partial().required({ id: true });

// For API Responses (all fields present)
export const AppointmentResponseSchema = AppointmentCoreSchema;

// For Database Queries (flexible field selection)
export const AppointmentQuerySchema = AppointmentCoreSchema.partial();

// ============================================================================
// 🔥 TYPE EXPORTS - Single Source of Truth Types
// ============================================================================

export type AppointmentStatus = z.infer<typeof AppointmentStatusSchema>;
export type Appointment = z.infer<typeof AppointmentCoreSchema>;
export type CreateAppointmentRequest = z.infer<typeof CreateAppointmentSchema>;
export type UpdateAppointmentRequest = z.infer<typeof UpdateAppointmentSchema>;
export type AppointmentResponse = z.infer<typeof AppointmentResponseSchema>;
export type AppointmentQuery = z.infer<typeof AppointmentQuerySchema>;

// ============================================================================
// 🎯 VALIDATION HELPERS
// ============================================================================

export const validateAppointment = (data: unknown): Appointment => {
  return AppointmentCoreSchema.parse(data);
};

export const validateCreateRequest = (data: unknown): CreateAppointmentRequest => {
  return CreateAppointmentSchema.parse(data);
};

export const validateUpdateRequest = (data: unknown): UpdateAppointmentRequest => {
  return UpdateAppointmentSchema.parse(data);
};

// ============================================================================
// 🔥 PRISMA SCHEMA GENERATOR
// ============================================================================

export const generatePrismaSchema = () => `
model Appointment {
  id                   String            @id @default(cuid())
  salonId              String
  customerId           String
  staffId              String?
  treatmentId          String
  
  // Customer info (denormalized)
  customerName         String
  customerEmail        String
  customerPhone        String?
  
  // Treatment info (denormalized)
  treatmentName        String
  treatmentDuration    Int
  treatmentPrice       Float
  
  // Salon info (denormalized)
  salonName            String
  salonLogo            String?
  salonColor           String?
  
  // Scheduling
  startTime            DateTime
  endTime              DateTime
  
  // Status & metadata
  status               AppointmentStatus @default(PENDING)
  notes                String?
  locale               String            @default("en-US")
  
  // Source tracking
  source               String            @default("planner")
  plannerAppointmentId String?
  
  // Timestamps
  createdAt            DateTime          @default(now())
  updatedAt            DateTime          @updatedAt
  confirmedAt          DateTime?
  completedAt          DateTime?
  cancelledAt          DateTime?

  @@map("appointments")
  @@index([salonId, startTime])
  @@index([customerId])
  @@index([status])
  @@index([staffId])
  @@index([treatmentId])
}

enum AppointmentStatus {
  PENDING
  CONFIRMED
  SCHEDULED
  IN_PROGRESS
  COMPLETED
  CANCELLED
  NO_SHOW
  RESCHEDULED
}
`;
