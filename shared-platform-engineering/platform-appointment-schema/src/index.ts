/**
 * 🎯 BEAUTY CRM APPOINTMENT SCHEMA - Single Source of Truth
 * 
 * This is the ONLY place where appointment schemas are defined.
 * All services import from here to eliminate duplication.
 * 
 * @example
 * ```typescript
 * // In your service
 * import { 
 *   Appointment, 
 *   CreateAppointmentRequest,
 *   validateAppointment,
 *   prismaToAppointment,
 *   createAppointmentCreatedEvent
 * } from '@beauty-crm/platform-appointment-schema';
 * 
 * // Use the types and validators
 * const appointment = validateAppointment(data);
 * const event = createAppointmentCreatedEvent(appointment, { source: 'my-service' });
 * ```
 */

// ============================================================================
// 🔥 CORE SCHEMA EXPORTS
// ============================================================================

export {
  // Core schemas
  AppointmentStatusSchema,
  AppointmentCoreSchema,
  CreateAppointmentSchema,
  UpdateAppointmentSchema,
  AppointmentResponseSchema,
  AppointmentQuerySchema,
  
  // Core types
  type AppointmentStatus,
  type Appointment,
  type CreateAppointmentRequest,
  type UpdateAppointmentRequest,
  type AppointmentResponse,
  type AppointmentQuery,
  
  // Validation helpers
  validateAppointment,
  validateCreateRequest,
  validateUpdateRequest,
  
  // Prisma schema generator
  generatePrismaSchema,
} from './core/appointment-schema';

// ============================================================================
// 🔄 ADAPTER EXPORTS
// ============================================================================

export {
  // Prisma adapters
  prismaToAppointment,
  appointmentToPrismaCreate,
  appointmentToPrismaUpdate,
  prismaToAppointments,
  buildAppointmentQuery,
} from './adapters/prisma-adapter';

export {
  // Domain adapters
  type AppointmentDomainModel,
  domainToAppointment,
  appointmentToDomain,
  domainToAppointments,
  appointmentsToDomain,
  createAppointmentDomain,
} from './adapters/domain-adapter';

// ============================================================================
// 📡 EVENT EXPORTS
// ============================================================================

export {
  // Event schemas
  BaseEventSchema,
  AppointmentCreatedEventSchema,
  AppointmentUpdatedEventSchema,
  AppointmentCancelledEventSchema,
  AppointmentConfirmedEventSchema,
  AppointmentCompletedEventSchema,
  AppointmentRescheduledEventSchema,
  AppointmentNoShowEventSchema,
  AppointmentEventSchema,
  
  // Event types
  type BaseEvent,
  type AppointmentCreatedEvent,
  type AppointmentUpdatedEvent,
  type AppointmentCancelledEvent,
  type AppointmentConfirmedEvent,
  type AppointmentCompletedEvent,
  type AppointmentRescheduledEvent,
  type AppointmentNoShowEvent,
  type AppointmentEvent,
  
  // Event creators
  createAppointmentCreatedEvent,
  createAppointmentUpdatedEvent,
  createAppointmentCancelledEvent,
  createAppointmentConfirmedEvent,
  createAppointmentCompletedEvent,
} from './events/appointment-events';

// ============================================================================
// 🎯 UTILITY EXPORTS
// ============================================================================

export const APPOINTMENT_AGGREGATE_TYPE = 'appointment' as const;

export const APPOINTMENT_EVENT_TYPES = {
  CREATED: 'appointment.created',
  UPDATED: 'appointment.updated',
  CANCELLED: 'appointment.cancelled',
  CONFIRMED: 'appointment.confirmed',
  COMPLETED: 'appointment.completed',
  RESCHEDULED: 'appointment.rescheduled',
  NO_SHOW: 'appointment.no_show',
} as const;

export const APPOINTMENT_STATUSES = {
  PENDING: 'PENDING',
  CONFIRMED: 'CONFIRMED',
  SCHEDULED: 'SCHEDULED',
  IN_PROGRESS: 'IN_PROGRESS',
  COMPLETED: 'COMPLETED',
  CANCELLED: 'CANCELLED',
  NO_SHOW: 'NO_SHOW',
  RESCHEDULED: 'RESCHEDULED',
} as const;

export const APPOINTMENT_SOURCES = {
  PLANNER: 'planner',
  MANAGEMENT: 'management',
  API: 'api',
  IMPORT: 'import',
} as const;

// ============================================================================
// 🔥 VERSION INFO
// ============================================================================

export const SCHEMA_VERSION = '1.0.0';
export const SCHEMA_NAME = '@beauty-crm/platform-appointment-schema';

/**
 * 🎉 Welcome message for the schema package
 */
export function welcome() {
  console.log(`
  🎯 Welcome to ${SCHEMA_NAME} v${SCHEMA_VERSION}
  
  Single Source of Truth for Appointment Schemas
  No more duplication across your DDD layers!
  
  Happy coding! ✨
  `);
}
