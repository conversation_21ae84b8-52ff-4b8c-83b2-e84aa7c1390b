/**
 * 🚀 REAL DADDY EXAMPLE - How to eliminate schema duplication in your services
 *
 * This shows how to refactor your existing appointment services to use
 * the Single Source of Truth schema instead of duplicated models.
 */

import {
  // Core types (replace all your duplicate interfaces)
  type Appointment,
  type CreateAppointmentRequest,
  type AppointmentStatus,
  // Validation (replace all your duplicate Zod schemas)
  validateAppointment,
  validateCreateRequest,
  // Prisma adapters (replace all your manual mapping)
  prismaToAppointment,
  appointmentToPrismaCreate,
  buildAppointmentQuery,
  // Domain adapters (replace your domain model duplication)
  type AppointmentDomainModel,
  appointmentToDomain,
  // Events (replace all your duplicate event schemas)
  createAppointmentCreatedEvent,
  AppointmentCreatedEvent,
  // Constants (replace scattered magic strings)
  APPOINTMENT_STATUSES,
  APPOINTMENT_EVENT_TYPES,
} from '@beauty-crm/platform-appointment-schema';

// ============================================================================
// 🔥 BEFORE: Your old duplicated approach
// ============================================================================

/*
// ❌ OLD WAY - Duplicated across every service:

// In appointment-planner-backend/src/domain/models/appointment.ts
export interface IAppointment {
  id?: string;
  salonId: string;
  customerName: string;
  // ... 30 more fields duplicated
}

// In appointment-management-backend/src/shared/types/appointment-events.ts
export const AppointmentSchema = z.object({
  id: z.string(),
  salonId: z.string(),
  // ... same 30 fields duplicated again
});

// In appointment-planner-backend/src/presentation/controllers/appointmentController.ts
const createAppointmentSchema = z.object({
  customerEmail: z.string().email(),
  // ... same fields duplicated AGAIN
});

// In both services: Manual Prisma mapping
const toDomain = (prisma: PrismaAppointment) => {
  return {
    id: prisma.id,
    salonId: prisma.salonId,
    // ... 30 lines of manual mapping duplicated in both services
  };
};
*/

// ============================================================================
// 🎯 AFTER: Single Source of Truth approach
// ============================================================================

// ✅ NEW WAY - Everything derives from one schema:

// 1. REPOSITORY LAYER - No more manual Prisma mapping
class AppointmentRepository {
  constructor(private prisma: any) {}

  async findById(id: string): Promise<Appointment | null> {
    const prismaAppointment = await this.prisma.appointment.findUnique({
      where: { id },
    });

    // ✨ Auto-conversion using adapter
    return prismaAppointment ? prismaToAppointment(prismaAppointment) : null;
  }

  async create(data: CreateAppointmentRequest): Promise<Appointment> {
    // ✨ Auto-conversion using adapter
    const prismaData = appointmentToPrismaCreate(data);
    const created = await this.prisma.appointment.create({ data: prismaData });
    return prismaToAppointment(created);
  }

  async findByFilters(filters: {
    salonId?: string;
    status?: AppointmentStatus;
    startDate?: Date;
    endDate?: Date;
  }): Promise<Appointment[]> {
    // ✨ Type-safe query builder
    const where = buildAppointmentQuery(filters);
    const appointments = await this.prisma.appointment.findMany({ where });
    return appointments.map(prismaToAppointment);
  }
}

// 2. DOMAIN LAYER - Focus on business logic, not mapping
class AppointmentDomain implements AppointmentDomainModel {
  constructor(private appointment: Appointment) {}

  // ✨ All properties auto-derived from core schema
  get id() {
    return this.appointment.id;
  }
  get salonId() {
    return this.appointment.salonId;
  }
  get customerId() {
    return this.appointment.customerId;
  }
  get status() {
    return this.appointment.status;
  }
  // ... all other properties automatically available

  // 🎯 Focus on BUSINESS LOGIC, not data mapping
  confirm(): void {
    if (this.status !== APPOINTMENT_STATUSES.PENDING) {
      throw new Error('Only pending appointments can be confirmed');
    }
    this.appointment = {
      ...this.appointment,
      status: APPOINTMENT_STATUSES.CONFIRMED,
      confirmedAt: new Date(),
    };
  }

  cancel(reason?: string): void {
    if (!this.canBeModified()) {
      throw new Error('Appointment cannot be cancelled');
    }
    this.appointment = {
      ...this.appointment,
      status: APPOINTMENT_STATUSES.CANCELLED,
      cancelledAt: new Date(),
    };
  }

  complete(): void {
    this.appointment = {
      ...this.appointment,
      status: APPOINTMENT_STATUSES.COMPLETED,
      completedAt: new Date(),
    };
  }

  reschedule(newStartTime: Date, newEndTime: Date): void {
    this.appointment = {
      ...this.appointment,
      startTime: newStartTime,
      endTime: newEndTime,
      status: APPOINTMENT_STATUSES.RESCHEDULED,
    };
  }

  isOverlapping(other: AppointmentDomainModel): boolean {
    return (
      this.startTime < other.endTime &&
      this.endTime > other.startTime &&
      this.salonId === other.salonId &&
      this.staffId === other.staffId
    );
  }

  canBeModified(): boolean {
    return ![
      APPOINTMENT_STATUSES.COMPLETED,
      APPOINTMENT_STATUSES.CANCELLED,
    ].includes(this.status);
  }

  getDuration(): number {
    return this.appointment.treatmentDuration;
  }

  getTimeSlot(): { start: Date; end: Date } {
    return {
      start: this.appointment.startTime,
      end: this.appointment.endTime,
    };
  }

  // ✨ Get the core data for persistence/events
  toAppointment(): Appointment {
    return this.appointment;
  }
}

// 3. SERVICE LAYER - Clean and focused
class AppointmentService {
  constructor(
    private repository: AppointmentRepository,
    private eventPublisher: any,
  ) {}

  async createAppointment(
    request: CreateAppointmentRequest,
  ): Promise<Appointment> {
    // ✨ Validation using single source of truth
    const validatedRequest = validateCreateRequest(request);

    // Create appointment
    const appointment = await this.repository.create(validatedRequest);

    // ✨ Event creation using single source of truth
    const event = createAppointmentCreatedEvent(appointment, {
      source: 'appointment-service',
      correlationId: 'req-123',
    });

    await this.eventPublisher.publish(event);

    return appointment;
  }

  async confirmAppointment(id: string): Promise<Appointment> {
    const appointment = await this.repository.findById(id);
    if (!appointment) {
      throw new Error('Appointment not found');
    }

    // ✨ Use domain model for business logic
    const domain = new AppointmentDomain(appointment);
    domain.confirm();

    // Save and return
    const updated = await this.repository.update(domain.toAppointment());
    return updated;
  }
}

// 4. CONTROLLER LAYER - Simple validation and response
class AppointmentController {
  constructor(private service: AppointmentService) {}

  async createAppointment(req: any, res: any) {
    try {
      // ✨ Single validation schema for all services
      const validatedData = validateCreateRequest(req.body);

      const appointment = await this.service.createAppointment(validatedData);

      // ✨ Consistent response format
      res.json({
        success: true,
        data: appointment,
      });
    } catch (error) {
      res.status(400).json({
        success: false,
        error: error.message,
      });
    }
  }
}

// ============================================================================
// 🎯 MIGRATION STRATEGY
// ============================================================================

/*
1. Install the schema package:
   bun add @beauty-crm/platform-appointment-schema

2. Replace imports in your services:
   - Remove duplicate interfaces/types
   - Remove duplicate Zod schemas  
   - Remove manual Prisma mapping
   - Import everything from the schema package

3. Update your Prisma schema:
   - Use generatePrismaSchema() or copy the generated schema
   - Run prisma db push

4. Update your services one by one:
   - Start with the repository layer
   - Then domain layer
   - Then service layer
   - Finally controller layer

5. Remove all duplicate code:
   - Delete old type definitions
   - Delete old validation schemas
   - Delete old mapping functions
   - Delete old event schemas
*/

export {
  AppointmentRepository,
  AppointmentDomain,
  AppointmentService,
  AppointmentController,
};
