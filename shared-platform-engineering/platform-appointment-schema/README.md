# 🎯 Beauty CRM Appointment Schema - Single Source of Truth

> **Real Daddy Solution** for eliminating schema duplication in DDD with Prisma + TypeScript + Node + Postgres

## 🔥 The Problem You Had

Your Beauty CRM was suffering from **massive schema duplication**:

```typescript
// ❌ BEFORE: Duplicated across EVERY service

// In appointment-planner-backend/src/domain/models/appointment.ts
export interface IAppointment {
  id?: string;
  salonId: string;
  customerName: string;
  // ... 30+ fields duplicated
}

// In appointment-management-backend/src/shared/types/appointment-events.ts  
export const AppointmentSchema = z.object({
  id: z.string(),
  salonId: z.string(),
  // ... SAME 30+ fields duplicated AGAIN
});

// In appointment-planner-backend/src/presentation/controllers/appointmentController.ts
const createAppointmentSchema = z.object({
  customerEmail: z.string().email(),
  // ... SAME fields duplicated AGAIN
});

// Manual Prisma mapping in BOTH services
const toDomain = (prisma: PrismaAppointment) => {
  return {
    id: prisma.id,
    salonId: prisma.salonId,
    // ... 30+ lines of manual mapping duplicated everywhere
  };
};
```

## ✅ The Real Daddy Solution

**One schema to rule them all!** Everything derives from a single source of truth:

```typescript
// ✅ AFTER: Single Source of Truth

import {
  // Core types (replaces ALL your duplicate interfaces)
  Appointment,
  CreateAppointmentRequest,
  AppointmentStatus,
  
  // Validation (replaces ALL your duplicate Zod schemas)
  validateAppointment,
  validateCreateRequest,
  
  // Prisma adapters (replaces ALL your manual mapping)
  prismaToAppointment,
  appointmentToPrismaCreate,
  
  // Events (replaces ALL your duplicate event schemas)
  createAppointmentCreatedEvent,
  
  // Constants (replaces scattered magic strings)
  APPOINTMENT_STATUSES,
  APPOINTMENT_EVENT_TYPES,
} from '@beauty-crm/platform-appointment-schema';
```

## 🚀 Installation & Setup

### 1. Install the Package

```bash
# In your service directories
bun add @beauty-crm/platform-appointment-schema
```

### 2. Update Your Prisma Schema

```prisma
// Copy this generated schema to your prisma/schema.prisma
model Appointment {
  id                   String            @id @default(cuid())
  salonId              String
  customerId           String
  staffId              String?
  treatmentId          String
  
  // Customer info (denormalized for performance)
  customerName         String
  customerEmail        String
  customerPhone        String?
  
  // Treatment info (denormalized for performance)
  treatmentName        String
  treatmentDuration    Int
  treatmentPrice       Float
  
  // Salon info (denormalized for performance)
  salonName            String
  salonLogo            String?
  salonColor           String?
  
  // Scheduling
  startTime            DateTime
  endTime              DateTime
  
  // Status & metadata
  status               AppointmentStatus @default(PENDING)
  notes                String?
  locale               String            @default("en-US")
  
  // Source tracking
  source               String            @default("planner")
  plannerAppointmentId String?
  
  // Timestamps
  createdAt            DateTime          @default(now())
  updatedAt            DateTime          @updatedAt
  confirmedAt          DateTime?
  completedAt          DateTime?
  cancelledAt          DateTime?

  @@map("appointments")
  @@index([salonId, startTime])
  @@index([customerId])
  @@index([status])
}

enum AppointmentStatus {
  PENDING
  CONFIRMED
  SCHEDULED
  IN_PROGRESS
  COMPLETED
  CANCELLED
  NO_SHOW
  RESCHEDULED
}
```

### 3. Refactor Your Services

#### Repository Layer - No More Manual Mapping!

```typescript
class AppointmentRepository {
  async findById(id: string): Promise<Appointment | null> {
    const prismaAppointment = await this.prisma.appointment.findUnique({
      where: { id }
    });
    
    // ✨ Auto-conversion using adapter
    return prismaAppointment ? prismaToAppointment(prismaAppointment) : null;
  }
  
  async create(data: CreateAppointmentRequest): Promise<Appointment> {
    // ✨ Auto-conversion using adapter
    const prismaData = appointmentToPrismaCreate(data);
    const created = await this.prisma.appointment.create({ data: prismaData });
    return prismaToAppointment(created);
  }
}
```

#### Domain Layer - Focus on Business Logic!

```typescript
class AppointmentDomain {
  constructor(private appointment: Appointment) {}
  
  // ✨ All properties auto-derived from core schema
  get id() { return this.appointment.id; }
  get status() { return this.appointment.status; }
  
  // 🎯 Focus on BUSINESS LOGIC, not data mapping
  confirm(): void {
    if (this.status !== APPOINTMENT_STATUSES.PENDING) {
      throw new Error('Only pending appointments can be confirmed');
    }
    this.appointment = {
      ...this.appointment,
      status: APPOINTMENT_STATUSES.CONFIRMED,
      confirmedAt: new Date(),
    };
  }
}
```

#### Service Layer - Clean and Focused!

```typescript
class AppointmentService {
  async createAppointment(request: CreateAppointmentRequest): Promise<Appointment> {
    // ✨ Validation using single source of truth
    const validatedRequest = validateCreateRequest(request);
    
    const appointment = await this.repository.create(validatedRequest);
    
    // ✨ Event creation using single source of truth
    const event = createAppointmentCreatedEvent(appointment, {
      source: 'appointment-service',
    });
    
    await this.eventPublisher.publish(event);
    return appointment;
  }
}
```

#### Controller Layer - Simple Validation!

```typescript
class AppointmentController {
  async createAppointment(req: any, res: any) {
    try {
      // ✨ Single validation schema for all services
      const validatedData = validateCreateRequest(req.body);
      
      const appointment = await this.service.createAppointment(validatedData);
      
      res.json({ success: true, data: appointment });
    } catch (error) {
      res.status(400).json({ success: false, error: error.message });
    }
  }
}
```

## 🎯 Migration Strategy

### Phase 1: Install & Build
1. `bun add @beauty-crm/platform-appointment-schema`
2. Update your Prisma schema
3. Run `prisma db push`

### Phase 2: Replace Imports
1. Remove duplicate interfaces/types
2. Remove duplicate Zod schemas  
3. Remove manual Prisma mapping
4. Import everything from the schema package

### Phase 3: Service by Service
1. Start with repository layer
2. Then domain layer
3. Then service layer
4. Finally controller layer

### Phase 4: Clean Up
1. Delete old type definitions
2. Delete old validation schemas
3. Delete old mapping functions
4. Delete old event schemas

## 🔥 Benefits

- **90% Less Code**: Eliminate thousands of lines of duplicate schema definitions
- **Type Safety**: Single source ensures consistency across all services
- **Auto-Validation**: Zod schemas generated from core schema
- **Auto-Mapping**: Prisma adapters handle all conversions
- **Auto-Events**: Event schemas derived from core schema
- **Maintainability**: Change schema once, update everywhere
- **DDD Compliance**: Clean separation of concerns

## 📦 What's Included

- **Core Schema**: Single source of truth for appointment structure
- **Validation**: Zod schemas for create/update/query operations
- **Prisma Adapters**: Auto-conversion between Prisma and domain types
- **Domain Adapters**: Bridge between core schema and domain models
- **Event Schemas**: Complete event definitions for NATS/eventing
- **Type Exports**: All TypeScript types you need
- **Constants**: Centralized enums and magic strings

## 🎉 Result

Your appointment model went from **scattered across 15+ files** to **one beautiful, maintainable schema**. 

No more hunting for duplicate definitions. No more manual mapping. No more inconsistencies.

**This is how real daddies handle DDD schema duplication!** 🔥
