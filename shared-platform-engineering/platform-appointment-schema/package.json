{"name": "@beauty-crm/platform-appointment-schema", "version": "1.0.0", "description": "Single source of truth for all appointment schemas, types, and validations", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "bun run clean && tsc", "clean": "rm -rf dist", "dev": "tsc --watch", "format": "biome format --write .", "lint": "biome lint .", "lint:fix": "biome lint --write .", "test": "vitest", "test:coverage": "vitest --coverage"}, "dependencies": {"zod": "^3.25.73", "@prisma/client": "^6.11.0"}, "devDependencies": {"@biomejs/biome": "^2.0.6", "@types/node": "^22.15.34", "typescript": "^5.8.3", "vitest": "^3.2.4"}, "keywords": ["appointment", "schema", "validation", "types", "beauty-crm", "ddd"], "license": "MIT"}