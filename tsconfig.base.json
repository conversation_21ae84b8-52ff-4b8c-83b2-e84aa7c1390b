{"compilerOptions": {"baseUrl": ".", "composite": true, "declaration": true, "declarationMap": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "incremental": true, "jsx": "react-jsx", "lib": ["DOM", "DOM.Iterable", "ES2022"], "module": "ES2022", "moduleResolution": "Node", "paths": {"@beauty-crm/platform-appointment-domain": ["shared-platform-engineering/platform-appointment-domain/src/index.ts"], "@beauty-crm/platform-introvertic-ui": ["shared-platform-engineering/platform-introvertic-ui/src/index.ts"], "@beauty-crm/shared-types": ["shared-platform-engineering/shared-types/src/index.ts"], "@beauty-crm/shared-utils": ["shared-platform-engineering/shared-utils/src/index.ts"], "@beauty-crm/product-appointment-types": ["shared-product-engineering/product-appointment-types/src/index.ts"], "@beauty-crm/platform-eventing": ["shared-platform-engineering/platform-eventing/src/index.ts"]}, "skipLibCheck": true, "sourceMap": true, "strict": true, "target": "ES2022", "types": []}, "exclude": ["**/node_modules", "**/dist", "**/__tests__/**/*"], "references": [{"path": "./shared-platform-engineering/tsconfig.base.json"}, {"path": "./shared-product-engineering/tsconfig.base.json"}, {"path": "./shared-ddd-layers/tsconfig.base.json"}]}