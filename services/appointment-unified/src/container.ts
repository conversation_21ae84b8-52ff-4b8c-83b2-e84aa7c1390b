/**
 * 🏗️ DEPENDENCY INJECTION CONTAINER
 * 
 * Sets up all dependencies using our appointment libraries
 */

import { PrismaClient } from '@prisma/client';
import { createAppointmentEventPublisher } from '@beauty-crm/platform-appointment-eventing';
import { AppointmentDomainService } from '@beauty-crm/platform-appointment-domain';
import { AppointmentRepository } from '@beauty-crm/platform-appointment-infrastructure';
import { AppointmentService } from './services/AppointmentService';
import { AppointmentController } from './controllers/AppointmentController';

// ============================================================================
// 🎯 CONTAINER CLASS
// ============================================================================

export class Container {
  private static instance: Container;
  
  // Infrastructure
  public readonly prisma: PrismaClient;
  public readonly eventPublisher: any;
  
  // Domain
  public readonly domainService: AppointmentDomainService;
  
  // Infrastructure
  public readonly repository: AppointmentRepository;
  
  // Application
  public readonly appointmentService: AppointmentService;
  
  // Presentation
  public readonly appointmentController: AppointmentController;

  private constructor() {
    // Initialize infrastructure
    this.prisma = new PrismaClient({
      log: ['query', 'info', 'warn', 'error'],
    });
    
    this.eventPublisher = createAppointmentEventPublisher({
      natsUrl: process.env.NATS_URL || 'nats://localhost:4222',
    });
    
    // Initialize domain
    this.domainService = new AppointmentDomainService();
    
    // Initialize infrastructure
    this.repository = new AppointmentRepository(this.prisma);
    
    // Initialize application
    this.appointmentService = new AppointmentService(
      this.repository,
      this.domainService,
      this.eventPublisher
    );
    
    // Initialize presentation
    this.appointmentController = new AppointmentController(this.appointmentService);
  }

  public static getInstance(): Container {
    if (!Container.instance) {
      Container.instance = new Container();
    }
    return Container.instance;
  }

  public async initialize(): Promise<void> {
    try {
      // Connect to database
      await this.prisma.$connect();
      console.log('✅ Database connected');
      
      // Initialize event publisher
      if (this.eventPublisher?.initialize) {
        await this.eventPublisher.initialize();
        console.log('✅ Event publisher initialized');
      }
      
      console.log('✅ Container initialized successfully');
    } catch (error) {
      console.error('❌ Container initialization failed:', error);
      throw error;
    }
  }

  public async shutdown(): Promise<void> {
    try {
      // Disconnect from database
      await this.prisma.$disconnect();
      console.log('✅ Database disconnected');
      
      // Shutdown event publisher
      if (this.eventPublisher?.shutdown) {
        await this.eventPublisher.shutdown();
        console.log('✅ Event publisher shutdown');
      }
      
      console.log('✅ Container shutdown successfully');
    } catch (error) {
      console.error('❌ Container shutdown failed:', error);
      throw error;
    }
  }
}
