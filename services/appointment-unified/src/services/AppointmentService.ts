/**
 * 🎯 APPOINTMENT APPLICATION SERVICE
 * 
 * Orchestrates the complete appointment flow using all our libraries:
 * - Schema library for validation
 * - Domain library for business logic
 * - Infrastructure library for persistence
 * - Eventing library for events
 */

import {
  Appointment,
  CreateAppointmentRequest,
  UpdateAppointmentRequest,
  validateCreateRequest,
  validateUpdateRequest,
  createAppointmentCreatedEvent,
  createAppointmentUpdatedEvent,
  createAppointmentConfirmedEvent,
  createAppointmentCancelledEvent,
  createAppointmentCompletedEvent,
  APPOINTMENT_STATUSES,
} from '@beauty-crm/platform-appointment-schema';

import {
  AppointmentDomain,
  AppointmentDomainService,
  AppointmentAggregate,
  createAppointmentDomain,
  type TimeSlot,
  type AppointmentFilters,
  type AppointmentStats,
} from '@beauty-crm/platform-appointment-domain';

import type { IAppointmentRepository } from '@beauty-crm/platform-appointment-infrastructure';

// ============================================================================
// 🎯 APPLICATION SERVICE
// ============================================================================

export class AppointmentService {
  constructor(
    private repository: IAppointmentRepository,
    private domainService: AppointmentDomainService,
    private eventPublisher: any
  ) {}

  // ============================================================================
  // 📝 APPOINTMENT CRUD OPERATIONS
  // ============================================================================

  async createAppointment(request: CreateAppointmentRequest): Promise<Appointment> {
    try {
      console.log('🎯 Creating appointment:', { customerId: request.customerId, treatmentName: request.treatmentName });
      
      // 1. Validate using schema library
      const validatedRequest = validateCreateRequest(request);
      console.log('✅ Request validated');
      
      // 2. Get existing appointments for conflict checking
      const existingAppointments = await this.repository.findBySalonId(
        validatedRequest.salonId,
        {
          staffId: validatedRequest.staffId,
          startDate: new Date(validatedRequest.startTime.getTime() - 24 * 60 * 60 * 1000), // 1 day before
          endDate: new Date(validatedRequest.startTime.getTime() + 24 * 60 * 60 * 1000), // 1 day after
        }
      );
      
      const existingDomains = existingAppointments.map(a => AppointmentDomain.fromAppointment(a));
      console.log(`✅ Found ${existingDomains.length} existing appointments for conflict check`);
      
      // 3. Use domain service to process creation with business rules
      const appointmentDomain = this.domainService.processAppointmentCreation(
        validatedRequest,
        existingDomains
      );
      console.log('✅ Domain validation passed');
      
      // 4. Save to database using infrastructure library
      const savedAppointment = await this.repository.create(validatedRequest);
      console.log('✅ Appointment saved to database:', savedAppointment.id);
      
      // 5. Publish event using eventing library
      const event = createAppointmentCreatedEvent(savedAppointment, {
        source: 'appointment-unified',
        userId: 'system', // In real app, get from auth context
      });
      
      if (this.eventPublisher?.publish) {
        await this.eventPublisher.publish(event);
        console.log('✅ Event published:', event.eventType);
      }
      
      console.log('🎉 Appointment created successfully:', savedAppointment.id);
      return savedAppointment;
      
    } catch (error) {
      console.error('❌ Failed to create appointment:', error);
      throw error;
    }
  }

  async getAppointmentById(id: string): Promise<Appointment | null> {
    console.log('🔍 Getting appointment by ID:', id);
    const appointment = await this.repository.findById(id);
    console.log(appointment ? '✅ Appointment found' : '❌ Appointment not found');
    return appointment;
  }

  async getAppointmentsBySalon(salonId: string, filters?: AppointmentFilters): Promise<Appointment[]> {
    console.log('🔍 Getting appointments for salon:', salonId, filters);
    const appointments = await this.repository.findBySalonId(salonId, filters);
    console.log(`✅ Found ${appointments.length} appointments`);
    return appointments;
  }

  async updateAppointment(request: UpdateAppointmentRequest): Promise<Appointment> {
    try {
      console.log('🔄 Updating appointment:', request.id);
      
      // 1. Validate using schema library
      const validatedRequest = validateUpdateRequest(request);
      
      // 2. Get existing appointment
      const existing = await this.repository.findById(validatedRequest.id);
      if (!existing) {
        throw new Error('Appointment not found');
      }
      
      // 3. Apply updates using domain logic
      const updatedData = { ...existing, ...validatedRequest };
      const updatedAppointment = await this.repository.update(updatedData);
      
      // 4. Publish event
      const event = createAppointmentUpdatedEvent(updatedAppointment, validatedRequest, {
        source: 'appointment-unified',
        userId: 'system',
        previousValues: existing,
      });
      
      if (this.eventPublisher?.publish) {
        await this.eventPublisher.publish(event);
      }
      
      console.log('✅ Appointment updated:', updatedAppointment.id);
      return updatedAppointment;
      
    } catch (error) {
      console.error('❌ Failed to update appointment:', error);
      throw error;
    }
  }

  // ============================================================================
  // 🎯 APPOINTMENT STATUS OPERATIONS
  // ============================================================================

  async confirmAppointment(id: string): Promise<Appointment> {
    try {
      console.log('✅ Confirming appointment:', id);
      
      const appointment = await this.repository.findById(id);
      if (!appointment) {
        throw new Error('Appointment not found');
      }
      
      // Use domain logic for confirmation
      const domain = AppointmentDomain.fromAppointment(appointment);
      domain.confirm();
      
      const updatedAppointment = await this.repository.update(domain.toAppointment());
      
      // Publish event
      const event = createAppointmentConfirmedEvent(id, {
        source: 'appointment-unified',
        userId: 'system',
      });
      
      if (this.eventPublisher?.publish) {
        await this.eventPublisher.publish(event);
      }
      
      console.log('✅ Appointment confirmed:', id);
      return updatedAppointment;
      
    } catch (error) {
      console.error('❌ Failed to confirm appointment:', error);
      throw error;
    }
  }

  async cancelAppointment(id: string, reason?: string): Promise<Appointment> {
    try {
      console.log('❌ Cancelling appointment:', id, reason);
      
      const appointment = await this.repository.findById(id);
      if (!appointment) {
        throw new Error('Appointment not found');
      }
      
      // Use domain logic for cancellation
      const domain = AppointmentDomain.fromAppointment(appointment);
      domain.cancel(reason);
      
      const updatedAppointment = await this.repository.update(domain.toAppointment());
      
      // Publish event
      const event = createAppointmentCancelledEvent(id, {
        source: 'appointment-unified',
        reason,
        userId: 'system',
      });
      
      if (this.eventPublisher?.publish) {
        await this.eventPublisher.publish(event);
      }
      
      console.log('✅ Appointment cancelled:', id);
      return updatedAppointment;
      
    } catch (error) {
      console.error('❌ Failed to cancel appointment:', error);
      throw error;
    }
  }

  async completeAppointment(id: string): Promise<Appointment> {
    try {
      console.log('🎉 Completing appointment:', id);
      
      const appointment = await this.repository.findById(id);
      if (!appointment) {
        throw new Error('Appointment not found');
      }
      
      // Use domain logic for completion
      const domain = AppointmentDomain.fromAppointment(appointment);
      domain.complete();
      
      const updatedAppointment = await this.repository.update(domain.toAppointment());
      
      // Publish event
      const event = createAppointmentCompletedEvent(id, {
        source: 'appointment-unified',
        userId: 'system',
      });
      
      if (this.eventPublisher?.publish) {
        await this.eventPublisher.publish(event);
      }
      
      console.log('✅ Appointment completed:', id);
      return updatedAppointment;
      
    } catch (error) {
      console.error('❌ Failed to complete appointment:', error);
      throw error;
    }
  }

  // ============================================================================
  // 📅 AVAILABILITY & ANALYTICS
  // ============================================================================

  async getAvailableSlots(salonId: string, date: Date, duration: number): Promise<TimeSlot[]> {
    console.log('📅 Getting available slots:', { salonId, date, duration });
    const slots = await this.repository.findAvailableSlots(salonId, date, duration);
    console.log(`✅ Found ${slots.filter(s => s.available).length} available slots`);
    return slots;
  }

  async getTodaysAppointments(salonId: string): Promise<Appointment[]> {
    console.log('📅 Getting today\'s appointments for salon:', salonId);
    const appointments = await this.repository.findTodaysAppointments(salonId);
    console.log(`✅ Found ${appointments.length} appointments for today`);
    return appointments;
  }

  async getAppointmentStats(salonId: string, startDate: Date, endDate: Date): Promise<AppointmentStats> {
    console.log('📊 Getting appointment stats:', { salonId, startDate, endDate });
    const stats = await this.repository.getAppointmentStats(salonId, startDate, endDate);
    console.log('✅ Stats calculated:', stats);
    return stats;
  }
}
