{"name": "@beauty-crm/appointment-unified", "version": "1.0.0", "description": "Unified Appointment Service - Complete appointment management using all our libraries", "type": "module", "main": "dist/index.js", "scripts": {"build": "bun run clean && tsc", "clean": "rm -rf dist", "dev": "bun run --hot src/index.ts", "start": "bun run dist/index.js", "test": "vitest", "test:coverage": "vitest --coverage", "format": "biome format --write .", "lint": "biome lint .", "lint:fix": "biome lint --write .", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio"}, "dependencies": {"@beauty-crm/platform-appointment-schema": "workspace:*", "@beauty-crm/platform-appointment-domain": "workspace:*", "@beauty-crm/platform-appointment-infrastructure": "workspace:*", "@beauty-crm/platform-appointment-eventing": "workspace:*", "@beauty-crm/platform-eventing": "workspace:*", "@hono/node-server": "^1.14.4", "@hono/zod-validator": "^0.7.0", "@paralleldrive/cuid2": "^2.2.2", "@prisma/client": "^6.11.1", "hono": "^4.8.3", "zod": "^3.25.73"}, "devDependencies": {"@biomejs/biome": "^2.0.6", "@types/node": "^22.15.34", "@vitest/coverage-v8": "^3.2.4", "bun-types": "^1.2.17", "prisma": "^6.11.1", "typescript": "^5.8.3", "vitest": "^3.2.4"}, "keywords": ["appointment", "beauty-crm", "booking", "scheduling", "salon", "unified", "complete"], "license": "MIT"}