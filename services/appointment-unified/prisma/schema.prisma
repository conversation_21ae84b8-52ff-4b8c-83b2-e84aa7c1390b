// 🎯 UNIFIED APPOINTMENT PRISMA SCHEMA
// Using the Single Source of Truth from @beauty-crm/platform-appointment-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Appointment {
  id                   String            @id @default(cuid())
  salonId              String
  customerId           String
  staffId              String?
  treatmentId          String
  
  // Customer info (denormalized for performance)
  customerName         String
  customerEmail        String
  customerPhone        String?
  
  // Treatment info (denormalized for performance)
  treatmentName        String
  treatmentDuration    Int
  treatmentPrice       Float
  
  // Salon info (denormalized for performance)
  salonName            String
  salonLogo            String?
  salonColor           String?
  
  // Scheduling
  startTime            DateTime
  endTime              DateTime
  
  // Status & metadata
  status               AppointmentStatus @default(PENDING)
  notes                String?
  locale               String            @default("en-US")
  
  // Source tracking
  source               String            @default("unified")
  plannerAppointmentId String?
  
  // Timestamps
  createdAt            DateTime          @default(now())
  updatedAt            DateTime          @updatedAt
  confirmedAt          DateTime?
  completedAt          DateTime?
  cancelledAt          DateTime?

  @@map("appointments")
  @@index([salonId, startTime])
  @@index([customerId])
  @@index([status])
  @@index([staffId])
  @@index([treatmentId])
}

enum AppointmentStatus {
  PENDING
  CONFIRMED
  SCHEDULED
  IN_PROGRESS
  COMPLETED
  CANCELLED
  NO_SHOW
  RESCHEDULED
}
