# Appointment Eventing Architecture

## Overview

This document describes the deep integration of the `@beauty-crm/platform-appointment-eventing` library into both appointment backends, implementing a robust event-driven architecture with reliable delivery guarantees.

## Architecture Components

### 1. Platform Libraries

#### `@beauty-crm/platform-appointment-eventing`
- **Event Creators**: `createAppointmentCreatedEvent`, `createAppointmentUpdatedEvent`, `createAppointmentCancelledEvent`
- **AppointmentEventPublisher**: High-level publisher for appointment events
- **PrismaTransactionalOutbox**: Reliable outbox implementation with retry logic

#### `@beauty-crm/platform-eventing`
- **EventPublisher**: Core NATS JetStream publisher
- **PublisherConfigs**: Pre-configured settings for appointment events
- **OutboxManager**: Base outbox processing functionality

### 2. Backend Implementations

#### Appointment Management Backend
```
src/
├── domain/
│   └── ports/
│       └── AppointmentEventing.ts          # Domain interfaces
├── infrastructure/
│   └── events/
│       ├── AppointmentEventPublisherImpl.ts # Domain adapter
│       ├── PrismaTransactionalOutbox.ts     # Re-export platform lib
│       ├── OutboxWorker.ts                  # Background processor
│       └── EventingContainer.ts             # DI container
└── services/
    └── appointment-service.ts               # Updated with eventing
```

#### Appointment Planner Backend
```
src/
├── infrastructure/
│   ├── events/
│   │   ├── AppointmentEventPublisherImpl.ts # Planner-specific adapter
│   │   └── EventingContainer.ts             # Planner DI container
│   └── outbox-worker.ts                     # Updated worker
└── domain/
    └── models/
        └── appointment.model.ts             # Updated with platform lib
```

## Event Flow

### 1. Transactional Outbox Pattern

```typescript
// Example: Creating an appointment with reliable eventing
async createAppointment(data: AppointmentProps): Promise<Appointment> {
  return this.prismaClient.$transaction(async (tx) => {
    // 1. Perform business operation
    const appointment = await this.repository.create(data);
    
    // 2. Store event in outbox (same transaction)
    await this.eventPublisher.publishAppointmentCreated(appointment, tx);
    
    // 3. Transaction commits both operations atomically
    return appointment;
  });
}
```

### 2. Background Processing

```typescript
// Outbox worker processes events every 5 seconds
const result = await outbox.processPendingEvents(
  publishEvent,  // Function to publish to NATS
  50,           // Batch size
  3             // Max retries
);
```

### 3. Cross-Service Communication

```
Customer Action → Planner Backend → Outbox → NATS → Management Backend
                                         ↓
                                   Other Services
                                   (Email, Notifications)
```

## Key Features

### ✅ Reliability
- **Transactional Outbox**: Events only published if business operations succeed
- **Automatic Retries**: Failed events retry up to 3 times with exponential backoff
- **Dead Letter Handling**: Failed events marked for manual intervention

### ✅ Consistency
- **ACID Transactions**: Event storage and business logic in same transaction
- **Event Ordering**: Events processed in chronological order
- **Idempotency**: Duplicate event handling prevented

### ✅ Observability
- **Comprehensive Logging**: All event operations logged with context
- **Health Checks**: NATS and database connectivity monitoring
- **Metrics**: Processing counts, failure rates, retry statistics

### ✅ Scalability
- **Asynchronous Processing**: API responses not blocked by event publishing
- **Batch Processing**: Multiple events processed efficiently
- **Horizontal Scaling**: Multiple worker instances supported

## Configuration

### Environment Variables
```bash
# NATS Configuration
NATS_URL=nats://localhost:4222
NATS_STREAM=APPOINTMENT_EVENTS

# Database Configuration
DATABASE_URL=postgresql://user:pass@localhost:5432/beauty_crm

# Worker Configuration
OUTBOX_BATCH_SIZE=50
OUTBOX_RETRY_LIMIT=3
OUTBOX_INTERVAL_MS=5000
```

### Docker Compose Integration
```yaml
services:
  appointment-management-backend:
    # ... service config
    depends_on:
      - nats
      - postgres
  
  appointment-management-outbox-worker:
    # ... worker config
    command: ["bun", "run", "worker"]
```

## Deployment

### 1. Database Setup
- Ensure outbox tables exist (`appointmentOutbox`, `outbox_events`)
- Configure proper indexes for performance
- Set up monitoring for table growth

### 2. NATS Configuration
- Create `APPOINTMENT_EVENTS` stream
- Configure retention policies
- Set up monitoring and alerting

### 3. Worker Deployment
- Deploy outbox workers as separate processes
- Configure health checks and restart policies
- Monitor processing metrics

## Monitoring & Troubleshooting

### Health Check Endpoints
```typescript
GET /health/eventing
{
  "status": "healthy",
  "details": {
    "nats": "connected",
    "database": "connected"
  }
}
```

### Common Issues
1. **Events Not Processing**: Check NATS connectivity and worker logs
2. **High Retry Counts**: Investigate downstream service health
3. **Outbox Growth**: Monitor and clean up processed events
4. **Duplicate Events**: Verify idempotency handling in consumers

## Migration Guide

### From Old Implementation
1. **Remove Old Files**: Deleted redundant `OutboxEvent.ts`, `OutboxRelayer.ts`
2. **Update Imports**: Use platform library exports
3. **Update Services**: Inject event publishers into domain services
4. **Deploy Workers**: Start new outbox workers
5. **Monitor**: Verify event flow and processing

### Testing
```bash
# Run integration tests
bun test src/**/*.integration.test.ts

# Test event publishing
curl -X POST /appointments -d '{"customerId":"123",...}'

# Verify outbox processing
SELECT * FROM appointmentOutbox WHERE processed = false;
```

## Future Enhancements

- **Event Sourcing**: Full event store implementation
- **Saga Patterns**: Complex workflow orchestration
- **Event Replay**: Historical event reprocessing
- **Schema Evolution**: Backward-compatible event versioning
