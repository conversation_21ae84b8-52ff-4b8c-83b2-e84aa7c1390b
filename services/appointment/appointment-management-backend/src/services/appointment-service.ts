import type { AppointmentProps } from '../domain/models/AppointmentModel';
import { Appointment } from '../domain/models/AppointmentModel';
import type { AppointmentRepository } from '../domain/repositories/appointment-repository';
import type { AppointmentEventPublisher } from '../domain/ports/AppointmentEventing';
import { PrismaClient } from '@prisma/client';

export class AppointmentService {
  constructor(
    private appointmentRepository: AppointmentRepository,
    private eventPublisher: AppointmentEventPublisher,
    private prismaClient: PrismaClient
  ) {}

  async getAllAppointments(): Promise<Appointment[]> {
    // For demonstration - in real implementation would have pagination
    const appointments = await this.appointmentRepository.findByStaffId('');
    return appointments;
  }

  async getAppointmentById(id: string): Promise<Appointment | null> {
    return this.appointmentRepository.findById(id);
  }

  async getAppointmentsByStaffId(staffId: string): Promise<Appointment[]> {
    return this.appointmentRepository.findByStaffId(staffId);
  }

  async getAppointmentsByDateRange(
    _startDate: Date,
    _endDate: Date
  ): Promise<Appointment[]> {
    // Simplified implementation - would need more sophisticated query
    return this.appointmentRepository.findByStaffId('');
  }

  async getAppointmentsByStaffAndDateRange(
    staffId: string,
    _startDate: Date,
    _endDate: Date
  ): Promise<Appointment[]> {
    // Simplified implementation
    return this.appointmentRepository.findByStaffId(staffId);
  }

  async createAppointment(
    appointmentData: AppointmentProps
  ): Promise<Appointment> {
    // Validate appointment data
    if (
      !appointmentData.customerId ||
      !appointmentData.staffId ||
      !appointmentData.treatmentId
    ) {
      throw new Error('Missing required appointment details');
    }

    // Ensure startTime and endTime are defined
    if (!appointmentData.startTime || !appointmentData.endTime) {
      throw new Error('Appointment start and end times are required');
    }

    const startTime =
      appointmentData.startTime instanceof Date
        ? appointmentData.startTime
        : new Date(appointmentData.startTime);
    const endTime =
      appointmentData.endTime instanceof Date
        ? appointmentData.endTime
        : new Date(appointmentData.endTime);

    // Check for conflicting appointments
    const conflictingAppointments = await this.appointmentRepository.findConflictingAppointments(
      appointmentData.staffId,
      startTime,
      endTime
    );

    if (conflictingAppointments.length > 0) {
      throw new Error('Appointment conflicts with existing appointments');
    }

    // Use transaction to ensure atomicity between appointment creation and event publishing
    return this.prismaClient.$transaction(async (tx) => {
      // Create a new appointment instance
      const appointment = new Appointment(appointmentData);
      const createdAppointment = await this.appointmentRepository.create(
        appointment
      );

      // Publish appointment created event using transactional outbox
      await this.eventPublisher.publishAppointmentCreated(
        createdAppointment as any, // Convert to UnifiedAppointment
        tx
      );

      return createdAppointment;
    });
  }

  async updateAppointment(
    appointmentId: string,
    updateData: Partial<AppointmentProps>
  ): Promise<Appointment> {
    const appointment = await this.appointmentRepository.findById(
      appointmentId
    );

    if (!appointment) {
      throw new Error('Appointment not found');
    }

    // Track changes for event publishing
    const changes = Object.keys(updateData);

    // Use transaction to ensure atomicity between appointment update and event publishing
    return this.prismaClient.$transaction(async (tx) => {
      // Update appointment properties
      Object.assign(appointment, updateData);
      appointment.updatedAt = new Date();

      const updatedAppointment = await this.appointmentRepository.update(
        appointment
      );

      // Publish appointment updated event using transactional outbox
      await this.eventPublisher.publishAppointmentUpdated(
        updatedAppointment as any, // Convert to UnifiedAppointment
        changes,
        tx
      );

      return updatedAppointment;
    });
  }

  async cancelAppointment(
    appointmentId: string,
    reason = 'Cancelled by management'
  ): Promise<Appointment> {
    const appointment = await this.appointmentRepository.findById(
      appointmentId
    );

    if (!appointment) {
      throw new Error('Appointment not found');
    }

    // Use transaction to ensure atomicity between appointment cancellation and event publishing
    return this.prismaClient.$transaction(async (tx) => {
      // Use domain method to cancel
      appointment.cancel();

      const cancelledAppointment = await this.appointmentRepository.update(
        appointment
      );

      // Publish appointment cancelled event using transactional outbox
      await this.eventPublisher.publishAppointmentCancelled(
        appointmentId,
        reason,
        tx
      );

      return cancelledAppointment;
    });
  }

  async remakeAppointment(
    appointmentId: string,
    newStartTime: Date,
    newEndTime: Date
  ): Promise<Appointment> {
    const appointment = await this.appointmentRepository.findById(
      appointmentId
    );

    if (!appointment) {
      throw new Error('Appointment not found');
    }

    // Check for conflicting appointments
    const conflictingAppointments = await this.appointmentRepository.findConflictingAppointments(
      appointment.staffId,
      newStartTime,
      newEndTime
    );

    if (conflictingAppointments.length > 0) {
      throw new Error(
        'New appointment time conflicts with existing appointments'
      );
    }

    // Use domain method to reschedule
    appointment.reschedule(newStartTime, newEndTime);

    return this.appointmentRepository.update(appointment);
  }

  async getCustomerAppointments(customerId: string): Promise<Appointment[]> {
    return this.appointmentRepository.findByCustomerId(customerId);
  }

  async getStaffAppointments(staffId: string): Promise<Appointment[]> {
    return this.appointmentRepository.findByStaffId(staffId);
  }
}
