/**
 * Example of how to wire up the new eventing infrastructure in the application container
 */

import { PrismaClient } from '@prisma/client';
import { getEventingContainer } from './infrastructure/events/EventingContainer';
import { AppointmentService } from './services/appointment-service';
import { PrismaAppointmentRepository } from './infrastructure/repositories/PrismaAppointmentRepository';

/**
 * @class ApplicationContainer
 * @description Example dependency injection container showing how to wire up
 * the new eventing infrastructure with existing services.
 */
export class ApplicationContainer {
  private static instance: ApplicationContainer;
  private prismaClient: PrismaClient;
  private appointmentService: AppointmentService;
  private appointmentRepository: PrismaAppointmentRepository;

  private constructor() {
    this.prismaClient = new PrismaClient();
    this.setupServices();
  }

  public static getInstance(): ApplicationContainer {
    if (!ApplicationContainer.instance) {
      ApplicationContainer.instance = new ApplicationContainer();
    }
    return ApplicationContainer.instance;
  }

  private setupServices(): void {
    // Get eventing infrastructure
    const eventingContainer = getEventingContainer();
    
    // Setup repository
    this.appointmentRepository = new PrismaAppointmentRepository(this.prismaClient);
    
    // Setup service with eventing
    this.appointmentService = new AppointmentService(
      this.appointmentRepository,
      eventingContainer.getAppointmentEventPublisher(),
      this.prismaClient,
    );
  }

  public getAppointmentService(): AppointmentService {
    return this.appointmentService;
  }

  public getPrismaClient(): PrismaClient {
    return this.prismaClient;
  }

  /**
   * Initialize the application container and all its dependencies.
   */
  public async initialize(): Promise<void> {
    try {
      // Initialize eventing infrastructure
      const eventingContainer = getEventingContainer();
      await eventingContainer.initialize();
      
      console.log('Application container initialized successfully');
    } catch (error) {
      console.error('Failed to initialize application container:', error);
      throw error;
    }
  }

  /**
   * Gracefully shutdown the application container.
   */
  public async shutdown(): Promise<void> {
    try {
      // Shutdown eventing infrastructure
      const eventingContainer = getEventingContainer();
      await eventingContainer.shutdown();
      
      // Close database connection
      await this.prismaClient.$disconnect();
      
      console.log('Application container shut down successfully');
    } catch (error) {
      console.error('Error during application container shutdown:', error);
      throw error;
    }
  }

  /**
   * Health check for the entire application.
   */
  public async healthCheck(): Promise<{
    status: 'healthy' | 'unhealthy';
    services: {
      eventing: 'healthy' | 'unhealthy';
      database: 'healthy' | 'unhealthy';
    };
  }> {
    const services = {
      eventing: 'unhealthy' as const,
      database: 'unhealthy' as const,
    };

    try {
      // Check eventing health
      const eventingContainer = getEventingContainer();
      const eventingHealth = await eventingContainer.healthCheck();
      services.eventing = eventingHealth.status;

      // Check database health
      await this.prismaClient.$queryRaw`SELECT 1`;
      services.database = 'healthy';
    } catch (error) {
      console.error('Health check failed:', error);
    }

    const status = services.eventing === 'healthy' && services.database === 'healthy'
      ? 'healthy'
      : 'unhealthy';

    return { status, services };
  }
}

/**
 * Example usage in main application file:
 * 
 * ```typescript
 * import { ApplicationContainer } from './container.example';
 * 
 * async function main() {
 *   const container = ApplicationContainer.getInstance();
 *   
 *   try {
 *     await container.initialize();
 *     
 *     // Use services
 *     const appointmentService = container.getAppointmentService();
 *     
 *     // Your application logic here...
 *     
 *   } catch (error) {
 *     console.error('Application failed to start:', error);
 *     process.exit(1);
 *   }
 * }
 * 
 * // Graceful shutdown
 * process.on('SIGINT', async () => {
 *   const container = ApplicationContainer.getInstance();
 *   await container.shutdown();
 *   process.exit(0);
 * });
 * 
 * main();
 * ```
 */
