import type { UnifiedAppointment } from '@beauty-crm/product-appointment-types';
import type { Prisma } from '@prisma/client';

// Re-export platform types for domain use
export type { AppointmentEvent } from '@beauty-crm/product-appointment-types';
export type { PrismaTransactionalOutbox as TransactionalOutbox } from '@beauty-crm/platform-appointment-eventing';

/**
 * @interface AppointmentEventPublisher
 * @description Defines the contract for publishing appointment-related domain events.
 * This port is used by the application layer to publish events without being
 * coupled to a specific event bus implementation.
 *
 * It relies on the TransactionalOutbox to ensure at-least-once delivery guarantees.
 */
export interface AppointmentEventPublisher {
  /**
   * Publishes an 'appointment.created' event.
   *
   * @param {UnifiedAppointment} appointment - The appointment data.
   * @param {Prisma.TransactionClient} tx - The Prisma transaction client for outbox integration.
   * @returns {Promise<void>}
   */
  publishAppointmentCreated(
    appointment: UnifiedAppointment,
    tx: Prisma.TransactionClient,
  ): Promise<void>;

  /**
   * Publishes an 'appointment.updated' event.
   *
   * @param {UnifiedAppointment} appointment - The updated appointment data.
   * @param {string[]} changes - A list of fields that were changed.
   * @param {Prisma.TransactionClient} tx - The Prisma transaction client for outbox integration.
   * @returns {Promise<void>}
   */
  publishAppointmentUpdated(
    appointment: UnifiedAppointment,
    changes: string[],
    tx: Prisma.TransactionClient,
  ): Promise<void>;

  /**
   * Publishes an 'appointment.cancelled' event.
   *
   * @param {string} appointmentId - The ID of the cancelled appointment.
   * @param {string} reason - The cancellation reason.
   * @param {Prisma.TransactionClient} tx - The Prisma transaction client for outbox integration.
   * @returns {Promise<void>}
   */
  publishAppointmentCancelled(
    appointmentId: string,
    reason: string,
    tx: Prisma.TransactionClient,
  ): Promise<void>;
}
