import {
  EventPublisher,
  PublisherConfigs,
} from '@beauty-crm/platform-eventing';
import { PrismaTransactionalOutbox } from '@beauty-crm/platform-appointment-eventing';
import type { AppointmentEvent } from '@beauty-crm/product-appointment-types';
import { PrismaClient } from '@prisma/client';
import { AppointmentEventPublisherImpl } from './AppointmentEventPublisherImpl';

/**
 * @class OutboxWorker
 * @description Background worker that processes events from the outbox and publishes them to NATS.
 * This ensures reliable event delivery using the outbox pattern.
 */
export class OutboxWorker {
  private eventPublisher: EventPublisher;
  private appointmentEventPublisher: AppointmentEventPublisherImpl;
  private outbox: PrismaTransactionalOutbox;
  private prismaClient: PrismaClient;
  private isRunning = false;
  private intervalId?: NodeJS.Timeout;

  constructor() {
    this.prismaClient = new PrismaClient();
    this.eventPublisher = new EventPublisher(
      PublisherConfigs.appointment('appointment-management-backend'),
    );
    this.outbox = new PrismaTransactionalOutbox(this.prismaClient);
    this.appointmentEventPublisher = new AppointmentEventPublisherImpl(
      this.eventPublisher,
      this.outbox,
    );
  }

  /**
   * Starts the outbox worker with the specified interval.
   *
   * @param intervalMs Processing interval in milliseconds (default: 5000ms)
   */
  public async start(intervalMs = 5000): Promise<void> {
    if (this.isRunning) {
      console.log('OutboxWorker is already running');
      return;
    }

    console.log('Starting OutboxWorker...');

    // Connect to NATS
    await this.eventPublisher.connect();

    this.isRunning = true;

    // Process immediately on start
    await this.processOutboxEvents();

    // Set up recurring processing
    this.intervalId = setInterval(async () => {
      if (this.isRunning) {
        await this.processOutboxEvents();
      }
    }, intervalMs);

    console.log(`OutboxWorker started with ${intervalMs}ms interval`);
  }

  /**
   * Stops the outbox worker.
   */
  public async stop(): Promise<void> {
    if (!this.isRunning) {
      console.log('OutboxWorker is not running');
      return;
    }

    console.log('Stopping OutboxWorker...');

    this.isRunning = false;

    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = undefined;
    }

    // Disconnect from NATS
    await this.eventPublisher.disconnect();

    // Close Prisma connection
    await this.prismaClient.$disconnect();

    console.log('OutboxWorker stopped');
  }

  /**
   * Processes pending events from the outbox.
   */
  private async processOutboxEvents(): Promise<void> {
    try {
      const result = await this.outbox.processPendingEvents(
        this.publishEvent.bind(this),
        50, // batch size
        3, // max retries
      );

      if (result.processed > 0 || result.failed > 0) {
        console.log(
          `OutboxWorker processed ${result.processed} events, ${result.failed} failed`,
        );
      }
    } catch (error) {
      console.error('Error processing outbox events:', error);
    }
  }

  /**
   * Publishes a single event to NATS.
   *
   * @param event The event to publish
   */
  private async publishEvent(event: AppointmentEvent): Promise<void> {
    try {
      // Extract appointment data from the event
      const appointmentData = this.extractAppointmentFromEvent(event);

      if (!appointmentData) {
        throw new Error(
          `Unable to extract appointment data from event: ${event.eventType}`,
        );
      }

      // Determine event type and publish accordingly
      switch (event.eventType) {
        case 'appointment.created':
          await this.appointmentEventPublisher.publishDirect(
            appointmentData,
            'created',
          );
          break;

        case 'appointment.updated':
          const changes = this.extractChangesFromEvent(event);
          await this.appointmentEventPublisher.publishDirect(
            appointmentData,
            'updated',
            { changes },
          );
          break;

        case 'appointment.cancelled':
          const reason = this.extractReasonFromEvent(event);
          await this.appointmentEventPublisher.publishDirect(
            appointmentData,
            'cancelled',
            { reason },
          );
          break;

        default:
          throw new Error(`Unknown event type: ${event.eventType}`);
      }

      console.log(
        `Successfully published event: ${event.eventId} (${event.eventType})`,
      );
    } catch (error) {
      console.error(`Failed to publish event ${event.eventId}:`, error);
      throw error;
    }
  }

  /**
   * Extracts appointment data from the event.
   */
  private extractAppointmentFromEvent(event: AppointmentEvent): any {
    if (event.data && typeof event.data === 'object') {
      // Handle different event data structures
      if ('appointment' in event.data) {
        return event.data.appointment;
      }

      // For cancelled events, we might need to reconstruct minimal appointment data
      if (
        event.eventType === 'appointment.cancelled' &&
        'appointmentId' in event.data
      ) {
        return {
          id: event.data.appointmentId,
          // Add other required fields as needed
        };
      }

      return event.data;
    }

    return null;
  }

  /**
   * Extracts changes from an updated event.
   */
  private extractChangesFromEvent(event: AppointmentEvent): string[] {
    if (
      event.data &&
      typeof event.data === 'object' &&
      'changes' in event.data
    ) {
      const changes = event.data.changes;
      if (typeof changes === 'object' && changes !== null) {
        return Object.keys(changes);
      }
    }

    return [];
  }

  /**
   * Extracts cancellation reason from a cancelled event.
   */
  private extractReasonFromEvent(event: AppointmentEvent): string {
    if (
      event.data &&
      typeof event.data === 'object' &&
      'reason' in event.data
    ) {
      return String(event.data.reason);
    }

    return 'No reason provided';
  }

  /**
   * Graceful shutdown handler.
   */
  public async gracefulShutdown(): Promise<void> {
    console.log('Received shutdown signal, stopping OutboxWorker...');
    await this.stop();
    process.exit(0);
  }
}

// Handle graceful shutdown
if (require.main === module) {
  const worker = new OutboxWorker();

  // Start the worker
  worker.start().catch((error) => {
    console.error('Failed to start OutboxWorker:', error);
    process.exit(1);
  });

  // Handle shutdown signals
  process.on('SIGINT', () => worker.gracefulShutdown());
  process.on('SIGTERM', () => worker.gracefulShutdown());
}
