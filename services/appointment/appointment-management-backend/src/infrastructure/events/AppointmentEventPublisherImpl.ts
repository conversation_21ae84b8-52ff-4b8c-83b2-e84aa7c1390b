import { AppointmentEventPublisher as PlatformAppointmentEventPublisher } from '@beauty-crm/platform-appointment-eventing';
import type { EventPublisher } from '@beauty-crm/platform-eventing';
import type { UnifiedAppointment } from '@beauty-crm/product-appointment-types';
import type { Prisma } from '@prisma/client';
import type { AppointmentEventPublisher, TransactionalOutbox } from '../../domain/ports/AppointmentEventing';

/**
 * @class AppointmentEventPublisherImpl
 * @description Implementation of AppointmentEventPublisher using the platform eventing library.
 * This adapter bridges the domain port with the platform eventing infrastructure.
 */
export class AppointmentEventPublisherImpl implements AppointmentEventPublisher {
  private platformPublisher: PlatformAppointmentEventPublisher;
  private outbox: TransactionalOutbox;

  constructor(
    eventPublisher: EventPublisher,
    outbox: TransactionalOutbox,
  ) {
    this.platformPublisher = new PlatformAppointmentEventPublisher(eventPublisher);
    this.outbox = outbox;
  }

  /**
   * Publishes an 'appointment.created' event using the transactional outbox pattern.
   *
   * @param appointment The appointment data
   * @param tx The Prisma transaction client for outbox integration
   */
  public async publishAppointmentCreated(
    appointment: UnifiedAppointment,
    tx: Prisma.TransactionClient,
  ): Promise<void> {
    // Create the event using the platform library
    const event = await this.createAppointmentCreatedEvent(appointment);
    
    // Store in outbox within the transaction
    await this.outbox.add(event, tx);
  }

  /**
   * Publishes an 'appointment.updated' event using the transactional outbox pattern.
   *
   * @param appointment The updated appointment data
   * @param changes A list of fields that were changed
   * @param tx The Prisma transaction client for outbox integration
   */
  public async publishAppointmentUpdated(
    appointment: UnifiedAppointment,
    changes: string[],
    tx: Prisma.TransactionClient,
  ): Promise<void> {
    // Create the event using the platform library
    const event = await this.createAppointmentUpdatedEvent(appointment, changes);
    
    // Store in outbox within the transaction
    await this.outbox.add(event, tx);
  }

  /**
   * Publishes an 'appointment.cancelled' event using the transactional outbox pattern.
   *
   * @param appointmentId The ID of the cancelled appointment
   * @param reason The cancellation reason
   * @param tx The Prisma transaction client for outbox integration
   */
  public async publishAppointmentCancelled(
    appointmentId: string,
    reason: string,
    tx: Prisma.TransactionClient,
  ): Promise<void> {
    // Create the event using the platform library
    const event = await this.createAppointmentCancelledEvent(appointmentId, reason);
    
    // Store in outbox within the transaction
    await this.outbox.add(event, tx);
  }

  /**
   * Direct publishing method for outbox processing (bypasses outbox storage).
   * Used by the outbox worker to actually publish events to NATS.
   */
  public async publishDirect(
    appointment: UnifiedAppointment,
    eventType: 'created' | 'updated' | 'cancelled',
    metadata?: { changes?: string[]; reason?: string },
  ): Promise<void> {
    const eventOptions = {
      source: 'appointment-management-backend',
      correlationId: `appointment-${appointment.id}`,
    };

    switch (eventType) {
      case 'created':
        await this.platformPublisher.publishAppointmentCreated(appointment, eventOptions);
        break;
      case 'updated':
        if (metadata?.changes) {
          const changes = this.convertChangesToPartialAppointment(metadata.changes, appointment);
          await this.platformPublisher.publishAppointmentUpdated(appointment, changes, eventOptions);
        }
        break;
      case 'cancelled':
        if (metadata?.reason) {
          await this.platformPublisher.publishAppointmentCancelled(
            appointment.id,
            metadata.reason,
            eventOptions,
          );
        }
        break;
    }
  }

  private async createAppointmentCreatedEvent(appointment: UnifiedAppointment) {
    const { createAppointmentCreatedEvent } = await import('@beauty-crm/platform-appointment-eventing');
    return createAppointmentCreatedEvent(appointment, {
      source: 'appointment-management-backend',
      correlationId: `appointment-${appointment.id}`,
    });
  }

  private async createAppointmentUpdatedEvent(appointment: UnifiedAppointment, changes: string[]) {
    const { createAppointmentUpdatedEvent } = await import('@beauty-crm/platform-appointment-eventing');
    const partialChanges = this.convertChangesToPartialAppointment(changes, appointment);
    return createAppointmentUpdatedEvent(appointment, partialChanges, {
      source: 'appointment-management-backend',
      correlationId: `appointment-${appointment.id}`,
    });
  }

  private async createAppointmentCancelledEvent(appointmentId: string, reason: string) {
    const { createAppointmentCancelledEvent } = await import('@beauty-crm/platform-appointment-eventing');
    return createAppointmentCancelledEvent(appointmentId, reason, {
      source: 'appointment-management-backend',
      correlationId: `appointment-${appointmentId}`,
    });
  }

  private convertChangesToPartialAppointment(changes: string[], appointment: UnifiedAppointment): Partial<UnifiedAppointment> {
    const partialChanges: Partial<UnifiedAppointment> = {};
    
    for (const field of changes) {
      if (field in appointment) {
        (partialChanges as any)[field] = (appointment as any)[field];
      }
    }
    
    return partialChanges;
  }
}
