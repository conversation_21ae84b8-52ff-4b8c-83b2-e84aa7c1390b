{"compilerOptions": {"allowImportingTsExtensions": false, "allowSyntheticDefaultImports": true, "baseUrl": ".", "esModuleInterop": true, "lib": ["ES2022", "DOM"], "module": "ES2022", "moduleResolution": "node", "outDir": "./dist", "paths": {"@/*": ["./src/*"], "@application/*": ["./src/application/*"], "@beauty-crm/platform-logger": ["../../../shared-platform-engineering/platform-logger/src/index.ts"], "@domain/*": ["./src/domain/*"], "@infrastructure/*": ["./src/infrastructure/*"], "@presentation/*": ["./src/presentation/*"], "@prisma/client": ["./node_modules/.prisma/client"]}, "resolveJsonModule": true, "rootDir": "./src", "skipLibCheck": true, "sourceMap": true, "strict": true, "target": "ES2022", "tsBuildInfoFile": "./.tsbuildinfo", "typeRoots": ["./node_modules/@types", "../../../node_modules/@types"], "types": ["node"]}, "exclude": ["node_modules", "dist", "__tests__/**/*", "**/*.test.ts", "**/tests/**", "../../../services/appointment/appointment-management-backend"], "extends": "../../../tsconfig.base.json", "include": ["src/**/*"], "references": [{"path": "../../../shared-platform-engineering/platform-logger"}, {"path": "../../../shared-product-engineering/product-domain-types"}, {"path": "../../../shared-product-engineering/product-identity-types"}]}