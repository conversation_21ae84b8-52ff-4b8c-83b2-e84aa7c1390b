/**
 * 🎯 COMPLETE APPOINTMENT SERVICE
 *
 * This is the ULTIMATE appointment creation service that uses ALL our libraries:
 *
 * 📦 LIBRARIES USED:
 * ✅ @beauty-crm/platform-appointment-schema - Single Source of Truth
 * ✅ @beauty-crm/platform-appointment-domain - Business Logic & Rules
 * ✅ @beauty-crm/platform-appointment-infrastructure - Repository & Database
 * ✅ @beauty-crm/platform-appointment-eventing - Event Publishing
 * ✅ @beauty-crm/platform-eventing - Core Event Infrastructure
 *
 * 🎯 COMPLETE FLOW:
 * Request → Validation → Business Rules → Conflict Check → Database → Events → Response
 */

import { PrismaClient } from '@prisma/client';
import { Hono } from 'hono';
import { zValidator } from '@hono/zod-validator';

// 🎯 ALL APPOINTMENT LIBRARIES
import {
  // Types & Validation
  type CreateAppointmentRequest,
  UpdateAppointmentRequest,
  type Appointment,
  AppointmentStatus,
  validateCreateRequest,
  validateUpdateRequest,
  // Schema Definitions
  CreateAppointmentSchema,
  UpdateAppointmentSchema,
  // Event Creators
  createAppointmentCreatedEvent,
  createAppointmentUpdatedEvent,
  createAppointmentConfirmedEvent,
  createAppointmentCancelledEvent,
  createAppointmentCompletedEvent,
  // Constants
  APPOINTMENT_STATUSES,
  APPOINTMENT_SOURCES,
} from '@beauty-crm/platform-appointment-schema';

import {
  // Domain Objects
  AppointmentDomain,
  AppointmentDomainService,
  AppointmentAggregate,
  // Types
  TimeSlot,
  AppointmentFilters,
  AppointmentStats,
  // Commands
  CreateAppointmentCommand,
  UpdateAppointmentCommand,
  ConfirmAppointmentCommand,
  CancelAppointmentCommand,
  CompleteAppointmentCommand,
} from '@beauty-crm/platform-appointment-domain';

import {
  // Repository
  AppointmentRepository,
  type IAppointmentRepository,
} from '@beauty-crm/platform-appointment-infrastructure';

import {
  // Event Publisher
  createAppointmentEventPublisher,
  type AppointmentEventPublisher,
} from '@beauty-crm/platform-appointment-eventing';

import {
  // Core Eventing
  type EventBus,
  createEventBus,
} from '@beauty-crm/platform-eventing';

// ============================================================================
// 🎯 COMPLETE APPOINTMENT SERVICE CLASS
// ============================================================================

class CompleteAppointmentService {
  private prisma: PrismaClient;
  private repository: IAppointmentRepository;
  private domainService: AppointmentDomainService;
  private eventPublisher: AppointmentEventPublisher;
  private eventBus: EventBus;
  private app: Hono;

  constructor() {
    // Initialize infrastructure
    this.prisma = new PrismaClient({
      log: ['query', 'info', 'warn', 'error'],
    });

    // Initialize domain
    this.domainService = new AppointmentDomainService();

    // Initialize repository
    this.repository = new AppointmentRepository(this.prisma);

    // Initialize eventing
    this.eventPublisher = createAppointmentEventPublisher({
      natsUrl: process.env.NATS_URL || 'nats://localhost:4222',
    });

    this.eventBus = createEventBus({
      natsUrl: process.env.NATS_URL || 'nats://localhost:4222',
    });

    // Initialize HTTP server
    this.app = new Hono();
    this.setupRoutes();
  }

  // ============================================================================
  // 🚀 COMPLETE APPOINTMENT CREATION FLOW
  // ============================================================================

  async createAppointment(request: CreateAppointmentRequest): Promise<{
    success: boolean;
    appointment?: Appointment;
    error?: string;
    events?: any[];
  }> {
    console.log('🎯 Starting COMPLETE appointment creation flow...');

    const events: any[] = [];

    try {
      // ============================================================================
      // 📝 STEP 1: VALIDATE USING SCHEMA LIBRARY
      // ============================================================================

      console.log('📝 Step 1: Schema validation...');
      const validatedRequest = validateCreateRequest(request);
      console.log('✅ Schema validation passed');

      // ============================================================================
      // 🧠 STEP 2: DOMAIN BUSINESS RULES
      // ============================================================================

      console.log('🧠 Step 2: Domain business rules...');

      // Get existing appointments for conflict checking
      const existingAppointments = await this.repository.findBySalonId(
        validatedRequest.salonId,
        {
          staffId: validatedRequest.staffId,
          startDate: new Date(
            validatedRequest.startTime.getTime() - 24 * 60 * 60 * 1000,
          ),
          endDate: new Date(
            validatedRequest.startTime.getTime() + 24 * 60 * 60 * 1000,
          ),
        },
      );

      const existingDomains = existingAppointments.map((a) =>
        AppointmentDomain.fromAppointment(a),
      );
      console.log(
        `✅ Found ${existingDomains.length} existing appointments for conflict check`,
      );

      // Use domain service for business validation
      const appointmentDomain = this.domainService.processAppointmentCreation(
        validatedRequest,
        existingDomains,
      );
      console.log('✅ Domain business rules passed');

      // ============================================================================
      // 🏗️ STEP 3: AGGREGATE PATTERN
      // ============================================================================

      console.log('🏗️ Step 3: Processing with aggregate...');

      const aggregate = new AppointmentAggregate();
      const aggregateResult = aggregate.handle({
        commandType: 'create-appointment',
        data: validatedRequest,
        correlationId: `create-${Date.now()}`,
        userId: 'system', // In real app, get from auth context
      });

      if (!aggregateResult.success) {
        throw new Error(
          `Aggregate validation failed: ${aggregateResult.error}`,
        );
      }

      events.push(...aggregateResult.events);
      console.log(
        `✅ Aggregate processed, generated ${aggregateResult.events.length} events`,
      );

      // ============================================================================
      // 💾 STEP 4: DATABASE PERSISTENCE
      // ============================================================================

      console.log('💾 Step 4: Database persistence...');

      const savedAppointment = await this.repository.create(validatedRequest);
      console.log(`✅ Appointment saved to database: ${savedAppointment.id}`);

      // ============================================================================
      // 📡 STEP 5: EVENT PUBLISHING
      // ============================================================================

      console.log('📡 Step 5: Event publishing...');

      // Create appointment created event
      const createdEvent = createAppointmentCreatedEvent(savedAppointment, {
        source: 'complete-appointment-service',
        userId: 'system',
        correlationId: `create-${savedAppointment.id}`,
      });

      events.push(createdEvent);

      // Publish all events
      for (const event of events) {
        if (this.eventPublisher?.publish) {
          await this.eventPublisher.publish(event);
          console.log(`✅ Published event: ${event.eventType}`);
        }

        if (this.eventBus?.publish) {
          await this.eventBus.publish(event.eventType, event);
          console.log(`✅ Published to event bus: ${event.eventType}`);
        }
      }

      // ============================================================================
      // 🎯 STEP 6: BUSINESS ANALYTICS
      // ============================================================================

      console.log('🎯 Step 6: Business analytics...');

      // Update salon stats
      const todaysAppointments = await this.repository.findTodaysAppointments(
        savedAppointment.salonId,
      );
      console.log(
        `✅ Salon now has ${todaysAppointments.length} appointments today`,
      );

      // Check availability impact
      const availableSlots = await this.repository.findAvailableSlots(
        savedAppointment.salonId,
        new Date(savedAppointment.startTime),
        savedAppointment.treatmentDuration,
      );
      console.log(
        `✅ ${
          availableSlots.filter((s) => s.available).length
        } slots still available`,
      );

      // ============================================================================
      // 🎉 SUCCESS RESPONSE
      // ============================================================================

      console.log('🎉 COMPLETE appointment creation successful!');

      return {
        success: true,
        appointment: savedAppointment,
        events: events.map((e) => ({
          type: e.eventType,
          timestamp: e.timestamp,
          appointmentId: savedAppointment.id,
        })),
      };
    } catch (error) {
      console.error('❌ Complete appointment creation failed:', error);

      // Publish error event
      const errorEvent = {
        eventType: 'appointment.creation.failed',
        data: {
          error: error instanceof Error ? error.message : 'Unknown error',
          request: validatedRequest || request,
        },
        timestamp: new Date(),
        source: 'complete-appointment-service',
      };

      if (this.eventPublisher?.publish) {
        await this.eventPublisher.publish(errorEvent);
      }

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        events: [errorEvent],
      };
    }
  }

  // ============================================================================
  // 🌐 HTTP ENDPOINTS
  // ============================================================================

  private setupRoutes() {
    // Health check
    this.app.get('/health', (c) => {
      return c.json({
        status: 'healthy',
        service: 'complete-appointment-service',
        libraries: [
          'platform-appointment-schema',
          'platform-appointment-domain',
          'platform-appointment-infrastructure',
          'platform-appointment-eventing',
        ],
        timestamp: new Date().toISOString(),
      });
    });

    // Create appointment endpoint
    this.app.post(
      '/appointments',
      zValidator('json', CreateAppointmentSchema),
      async (c) => {
        const request = c.req.valid('json');
        const result = await this.createAppointment(request);

        if (result.success) {
          return c.json(
            {
              success: true,
              data: result.appointment,
              events: result.events,
            },
            201,
          );
        }
        return c.json(
          {
            success: false,
            error: result.error,
            events: result.events,
          },
          400,
        );
      },
    );

    // Get appointment by ID
    this.app.get('/appointments/:id', async (c) => {
      const id = c.req.param('id');
      const appointment = await this.repository.findById(id);

      if (appointment) {
        return c.json({
          success: true,
          data: appointment,
        });
      }
      return c.json(
        {
          success: false,
          error: 'Appointment not found',
        },
        404,
      );
    });

    // Get appointments by salon
    this.app.get('/salons/:salonId/appointments', async (c) => {
      const salonId = c.req.param('salonId');
      const appointments = await this.repository.findBySalonId(salonId);

      return c.json({
        success: true,
        data: appointments,
        count: appointments.length,
      });
    });
  }

  // ============================================================================
  // 🚀 SERVER LIFECYCLE
  // ============================================================================

  async start(port = 3001) {
    try {
      // Connect to database
      await this.prisma.$connect();
      console.log('✅ Database connected');

      // Initialize event systems
      if (this.eventPublisher?.initialize) {
        await this.eventPublisher.initialize();
        console.log('✅ Event publisher initialized');
      }

      if (this.eventBus?.initialize) {
        await this.eventBus.initialize();
        console.log('✅ Event bus initialized');
      }

      console.log(
        `🚀 Complete Appointment Service starting on port ${port}...`,
      );
      console.log('📦 Using ALL appointment libraries:');
      console.log('  ✅ platform-appointment-schema');
      console.log('  ✅ platform-appointment-domain');
      console.log('  ✅ platform-appointment-infrastructure');
      console.log('  ✅ platform-appointment-eventing');

      return this.app;
    } catch (error) {
      console.error('❌ Failed to start Complete Appointment Service:', error);
      throw error;
    }
  }

  async stop() {
    try {
      await this.prisma.$disconnect();
      if (this.eventPublisher?.shutdown) {
        await this.eventPublisher.shutdown();
      }
      if (this.eventBus?.shutdown) {
        await this.eventBus.shutdown();
      }
      console.log('✅ Complete Appointment Service stopped');
    } catch (error) {
      console.error('❌ Error stopping service:', error);
      throw error;
    }
  }

  getApp() {
    return this.app;
  }
}

// ============================================================================
// 🚀 EXPORT FOR EASY USAGE
// ============================================================================

export { CompleteAppointmentService };

// ============================================================================
// 🎯 STANDALONE SERVER (if run directly)
// ============================================================================

if (import.meta.main) {
  const service = new CompleteAppointmentService();

  service
    .start(3001)
    .then((app) => {
      console.log('🎉 Complete Appointment Service is running!');
      console.log('📋 Available endpoints:');
      console.log('  GET  /health');
      console.log('  POST /appointments');
      console.log('  GET  /appointments/:id');
      console.log('  GET  /salons/:salonId/appointments');
      console.log('\n🧪 Test with:');
      console.log('  curl http://localhost:3001/health');
    })
    .catch((error) => {
      console.error('❌ Failed to start service:', error);
      process.exit(1);
    });

  // Graceful shutdown
  process.on('SIGINT', async () => {
    console.log('\n🛑 Shutting down Complete Appointment Service...');
    await service.stop();
    process.exit(0);
  });
}
