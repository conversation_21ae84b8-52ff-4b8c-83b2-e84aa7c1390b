import {
  EventPublisher,
  PublisherConfigs,
} from '@beauty-crm/platform-eventing';
import { PrismaClient } from '@prisma/client';
import { AppointmentEventPublisherImpl } from './events/AppointmentEventPublisherImpl';

// Define the outbox event structure based on the planner backend schema
interface OutboxEvent {
  id: string;
  aggregateId: string;
  aggregateType: string;
  eventType: string;
  eventId: string;
  eventVersion: number;
  eventData: any;
  eventMetadata: any;
  source: string;
  timestamp: Date;
  processedAt?: Date | null;
  retryCount: number;
}

const prisma = new PrismaClient();
const eventPublisher = new EventPublisher({
  serviceName: 'appointment-planner-backend',
  stream: {
    name: 'APPOINTMENT_EVENTS',
    subjects: ['appointment.events.*'],
    description: 'All appointment lifecycle events',
  },
});
const appointmentEventPublisher = new AppointmentEventPublisherImpl(
  eventPublisher,
);

let shuttingDown = false;

async function publishEvent(event: OutboxEvent): Promise<void> {
  try {
    const appointmentData = extractAppointmentFromEvent(event);

    if (!appointmentData) {
      throw new Error(
        `Unable to extract appointment data from event: ${event.eventType}`,
      );
    }

    const metadata = {
      correlationId: event.eventId,
      userId: event.eventMetadata?.userId,
    };

    switch (event.eventType) {
      case 'appointment.created':
        await appointmentEventPublisher.publishAppointmentCreated(
          appointmentData,
          metadata,
        );
        break;
      case 'appointment.updated':
        const changes = extractChangesFromEvent(event);
        await appointmentEventPublisher.publishAppointmentUpdated(
          appointmentData,
          changes,
          metadata,
        );
        break;
      case 'appointment.cancelled':
        const reason = extractReasonFromEvent(event);
        await appointmentEventPublisher.publishAppointmentCancelled(
          appointmentData.id,
          reason,
          metadata,
        );
        break;
      default:
        throw new Error(`Unknown event type: ${event.eventType}`);
    }
  } catch (error) {
    console.error(`Failed to publish event ${event.eventId}:`, error);
    throw error;
  }
}

function extractAppointmentFromEvent(event: OutboxEvent): any {
  if (event.eventData && typeof event.eventData === 'object') {
    if ('appointment' in event.eventData) {
      return event.eventData.appointment;
    }
    return event.eventData;
  }
  return null;
}

function extractChangesFromEvent(event: OutboxEvent): any {
  if (
    event.eventData &&
    typeof event.eventData === 'object' &&
    'changes' in event.eventData
  ) {
    return event.eventData.changes;
  }
  return {};
}

function extractReasonFromEvent(event: OutboxEvent): string {
  if (
    event.eventData &&
    typeof event.eventData === 'object' &&
    'reason' in event.eventData
  ) {
    return String(event.eventData.reason);
  }
  return 'No reason provided';
}

async function processOutboxEvents(): Promise<{
  processed: number;
  failed: number;
}> {
  const batchSize = 50;
  const maxRetries = 3;

  // Get pending events from the outbox
  const pendingEvents = await prisma.appointmentOutbox.findMany({
    where: {
      processedAt: null,
      retryCount: { lt: maxRetries },
    },
    take: batchSize,
    orderBy: { timestamp: 'asc' },
  });

  let processed = 0;
  let failed = 0;

  for (const event of pendingEvents) {
    try {
      await publishEvent(event);

      // Mark as processed
      await prisma.appointmentOutbox.update({
        where: { id: event.id },
        data: { processedAt: new Date() },
      });

      processed++;
    } catch (error) {
      console.error(`Failed to process outbox event ${event.id}:`, error);

      // Increment retry count
      await prisma.appointmentOutbox.update({
        where: { id: event.id },
        data: { retryCount: event.retryCount + 1 },
      });

      failed++;
    }
  }

  return { processed, failed };
}

async function main() {
  console.log('Starting appointment planner outbox worker...');
  await eventPublisher.connect();
  console.log('NATS publisher connected.');

  while (!shuttingDown) {
    try {
      const result = await processOutboxEvents();
      if (result.processed > 0 || result.failed > 0) {
        console.log(
          `Processed ${result.processed} events, ${result.failed} failed.`,
        );
      }
    } catch (error) {
      console.error('Error processing outbox events:', error);
    }
    // Wait for a bit before polling again
    await new Promise((resolve) => setTimeout(resolve, 5000));
  }

  console.log('Outbox worker shutting down.');
  await eventPublisher.disconnect();
  await prisma.$disconnect();
}

function gracefulShutdown() {
  if (shuttingDown) return;
  shuttingDown = true;
  console.log('Received shutdown signal. Finishing current batch...');
}

process.on('SIGTERM', gracefulShutdown);
process.on('SIGINT', gracefulShutdown);

main().catch((err) => {
  console.error('Worker crashed:', err);
  process.exit(1);
});
