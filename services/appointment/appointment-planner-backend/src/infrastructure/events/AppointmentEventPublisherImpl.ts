import { AppointmentEventPublisher as PlatformAppointmentEventPublisher } from '@beauty-crm/platform-appointment-eventing';
import type { EventPublisher } from '@beauty-crm/platform-eventing';
import type { Appointment } from '@beauty-crm/product-appointment-types/src/appointment/types';
import type { Prisma } from '@prisma/client';

/**
 * @class AppointmentEventPublisherImpl
 * @description Implementation of AppointmentEventPublisher for the planner backend.
 * This adapter bridges the planner domain with the platform eventing infrastructure.
 */
export class AppointmentEventPublisherImpl {
  private platformPublisher: PlatformAppointmentEventPublisher;

  constructor(eventPublisher: EventPublisher) {
    this.platformPublisher = new PlatformAppointmentEventPublisher(eventPublisher);
  }

  /**
   * Publishes an 'appointment.created' event from the planner backend.
   *
   * @param appointment The appointment data
   * @param metadata Additional event metadata
   */
  public async publishAppointmentCreated(
    appointment: Appointment,
    metadata?: { correlationId?: string; userId?: string },
  ): Promise<void> {
    const eventOptions = {
      source: 'appointment-planner-backend',
      correlationId: metadata?.correlationId || `planner-appointment-${appointment.id}`,
      userId: metadata?.userId,
    };

    await this.platformPublisher.publishAppointmentCreated(appointment, eventOptions);
  }

  /**
   * Publishes an 'appointment.updated' event from the planner backend.
   *
   * @param appointment The updated appointment data
   * @param changes The changes made to the appointment
   * @param metadata Additional event metadata
   */
  public async publishAppointmentUpdated(
    appointment: Appointment,
    changes: Partial<Appointment>,
    metadata?: { correlationId?: string; userId?: string },
  ): Promise<void> {
    const eventOptions = {
      source: 'appointment-planner-backend',
      correlationId: metadata?.correlationId || `planner-appointment-${appointment.id}`,
      userId: metadata?.userId,
    };

    await this.platformPublisher.publishAppointmentUpdated(appointment, changes, eventOptions);
  }

  /**
   * Publishes an 'appointment.cancelled' event from the planner backend.
   *
   * @param appointmentId The ID of the cancelled appointment
   * @param reason The cancellation reason
   * @param metadata Additional event metadata
   */
  public async publishAppointmentCancelled(
    appointmentId: string,
    reason: string,
    metadata?: { correlationId?: string; userId?: string },
  ): Promise<void> {
    const eventOptions = {
      source: 'appointment-planner-backend',
      correlationId: metadata?.correlationId || `planner-appointment-${appointmentId}`,
      userId: metadata?.userId,
    };

    await this.platformPublisher.publishAppointmentCancelled(appointmentId, reason, eventOptions);
  }

  /**
   * Publishes appointment booking confirmation event.
   * This is specific to the planner backend workflow.
   *
   * @param appointment The booked appointment
   * @param customerEmail Customer's email for confirmation
   * @param metadata Additional event metadata
   */
  public async publishAppointmentBooked(
    appointment: Appointment,
    customerEmail: string,
    metadata?: { correlationId?: string; userId?: string },
  ): Promise<void> {
    // Create a custom booking event that extends the created event
    const eventOptions = {
      source: 'appointment-planner-backend',
      correlationId: metadata?.correlationId || `booking-${appointment.id}`,
      userId: metadata?.userId,
      customerEmail,
      bookingTimestamp: new Date().toISOString(),
    };

    await this.platformPublisher.publishAppointmentCreated(appointment, eventOptions);
  }

  /**
   * Publishes appointment availability change event.
   * This is specific to the planner backend when slots become available/unavailable.
   *
   * @param salonId The salon ID
   * @param timeSlot The affected time slot
   * @param available Whether the slot is now available
   * @param metadata Additional event metadata
   */
  public async publishAvailabilityChanged(
    salonId: string,
    timeSlot: { start: Date; end: Date },
    available: boolean,
    metadata?: { correlationId?: string; staffId?: string },
  ): Promise<void> {
    // This could be extended to use a custom availability event
    // For now, we'll use the appointment updated pattern
    const eventOptions = {
      source: 'appointment-planner-backend',
      correlationId: metadata?.correlationId || `availability-${salonId}-${timeSlot.start.getTime()}`,
      staffId: metadata?.staffId,
      availabilityChange: {
        salonId,
        timeSlot,
        available,
        timestamp: new Date().toISOString(),
      },
    };

    // Create a synthetic appointment update for availability tracking
    const syntheticAppointment: Appointment = {
      id: `availability-${salonId}-${timeSlot.start.getTime()}`,
      salonId,
      customerId: 'system',
      treatmentId: 'availability-change',
      staffId: metadata?.staffId || 'system',
      timeSlot,
      status: available ? 'AVAILABLE' : 'UNAVAILABLE',
      priority: 'low',
      createdAt: new Date(),
      updatedAt: new Date(),
    } as Appointment;

    const changes = { status: syntheticAppointment.status };
    await this.platformPublisher.publishAppointmentUpdated(syntheticAppointment, changes, eventOptions);
  }
}
