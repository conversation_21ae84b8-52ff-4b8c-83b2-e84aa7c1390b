import { EventPublisher, PublisherConfigs } from '@beauty-crm/platform-eventing';
import { PrismaTransactionalOutbox } from '@beauty-crm/platform-appointment-eventing';
import { PrismaClient } from '@prisma/client';
import { AppointmentEventPublisherImpl } from './AppointmentEventPublisherImpl';

/**
 * @class PlannerEventingContainer
 * @description Dependency injection container for planner backend eventing infrastructure.
 * Provides configured instances of event publishers and outbox components.
 */
export class PlannerEventingContainer {
  private static instance: PlannerEventingContainer;
  private eventPublisher: EventPublisher;
  private appointmentEventPublisher: AppointmentEventPublisherImpl;
  private transactionalOutbox: PrismaTransactionalOutbox;
  private prismaClient: PrismaClient;

  private constructor() {
    // Initialize Prisma client
    this.prismaClient = new PrismaClient();

    // Initialize event publisher with planner configuration
    this.eventPublisher = new EventPublisher(
      PublisherConfigs.appointment('appointment-planner-backend'),
    );

    // Initialize transactional outbox (using platform library)
    this.transactionalOutbox = new PrismaTransactionalOutbox(
      this.prismaClient,
      'outbox_events',
    );

    // Initialize appointment event publisher
    this.appointmentEventPublisher = new AppointmentEventPublisherImpl(
      this.eventPublisher,
    );
  }

  /**
   * Gets the singleton instance of the planner eventing container.
   */
  public static getInstance(): PlannerEventingContainer {
    if (!PlannerEventingContainer.instance) {
      PlannerEventingContainer.instance = new PlannerEventingContainer();
    }
    return PlannerEventingContainer.instance;
  }

  /**
   * Gets the configured event publisher.
   */
  public getEventPublisher(): EventPublisher {
    return this.eventPublisher;
  }

  /**
   * Gets the configured appointment event publisher.
   */
  public getAppointmentEventPublisher(): AppointmentEventPublisherImpl {
    return this.appointmentEventPublisher;
  }

  /**
   * Gets the configured transactional outbox.
   */
  public getTransactionalOutbox(): PrismaTransactionalOutbox {
    return this.transactionalOutbox;
  }

  /**
   * Gets the Prisma client instance.
   */
  public getPrismaClient(): PrismaClient {
    return this.prismaClient;
  }

  /**
   * Initializes the eventing infrastructure (connects to NATS).
   */
  public async initialize(): Promise<void> {
    try {
      await this.eventPublisher.connect();
      console.log('Planner eventing infrastructure initialized successfully');
    } catch (error) {
      console.error('Failed to initialize planner eventing infrastructure:', error);
      throw error;
    }
  }

  /**
   * Shuts down the eventing infrastructure gracefully.
   */
  public async shutdown(): Promise<void> {
    try {
      await this.eventPublisher.disconnect();
      await this.prismaClient.$disconnect();
      console.log('Planner eventing infrastructure shut down successfully');
    } catch (error) {
      console.error('Error during planner eventing infrastructure shutdown:', error);
      throw error;
    }
  }

  /**
   * Health check for the eventing infrastructure.
   */
  public async healthCheck(): Promise<{
    status: 'healthy' | 'unhealthy';
    details: {
      nats: 'connected' | 'disconnected';
      database: 'connected' | 'disconnected';
    };
  }> {
    const details = {
      nats: 'disconnected' as const,
      database: 'disconnected' as const,
    };

    try {
      // Check NATS connection
      const natsStatus = this.eventPublisher.getStatus();
      details.nats = natsStatus === 'connected' ? 'connected' : 'disconnected';

      // Check database connection
      await this.prismaClient.$queryRaw`SELECT 1`;
      details.database = 'connected';
    } catch (error) {
      console.error('Planner health check failed:', error);
    }

    const status = details.nats === 'connected' && details.database === 'connected'
      ? 'healthy'
      : 'unhealthy';

    return { status, details };
  }

  /**
   * Stores an event in the outbox for later processing.
   * This is useful for the planner backend when it needs to use the outbox pattern.
   */
  public async storeEventInOutbox(
    eventType: 'created' | 'updated' | 'cancelled',
    appointmentData: any,
    metadata?: { changes?: any; reason?: string; correlationId?: string; userId?: string },
  ): Promise<void> {
    const { createAppointmentCreatedEvent, createAppointmentUpdatedEvent, createAppointmentCancelledEvent } = 
      await import('@beauty-crm/platform-appointment-eventing');

    const eventOptions = {
      source: 'appointment-planner-backend',
      correlationId: metadata?.correlationId || `planner-${appointmentData.id}`,
      userId: metadata?.userId,
    };

    let event;
    switch (eventType) {
      case 'created':
        event = createAppointmentCreatedEvent(appointmentData, eventOptions);
        break;
      case 'updated':
        event = createAppointmentUpdatedEvent(appointmentData, metadata?.changes || {}, eventOptions);
        break;
      case 'cancelled':
        event = createAppointmentCancelledEvent(appointmentData.id, metadata?.reason || 'Cancelled', eventOptions);
        break;
      default:
        throw new Error(`Unknown event type: ${eventType}`);
    }

    // Store in outbox (this will be processed by the outbox worker)
    await this.transactionalOutbox.add(event, this.prismaClient);
  }
}

/**
 * Convenience function to get the planner eventing container instance.
 */
export function getPlannerEventingContainer(): PlannerEventingContainer {
  return PlannerEventingContainer.getInstance();
}
