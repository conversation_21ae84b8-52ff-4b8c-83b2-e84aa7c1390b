import { PrismaClient } from '@prisma/client';

export class OutboxRelayer {
  constructor(private prisma: PrismaClient) {}

  async start(natsUrl: string): Promise<void> {
    console.log(`Starting OutboxRelayer with NATS URL: ${natsUrl}`);
    // TODO: Implement NATS connection and subscription
  }

  async stop(): Promise<void> {
    console.log('Stopping OutboxRelayer');
    // TODO: Implement NATS disconnection
  }
}
