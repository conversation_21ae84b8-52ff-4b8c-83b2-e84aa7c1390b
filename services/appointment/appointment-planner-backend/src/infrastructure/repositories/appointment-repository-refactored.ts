/**
 * 🔥 REFACTORED APPOINTMENT REPOSITORY - Using Single Source of Truth
 *
 * This eliminates ALL manual Prisma mapping and uses the schema library adapters.
 * No more duplicate conversion logic across services!
 */

import {
  // Core types
  Appointment,
  CreateAppointmentRequest,
  AppointmentStatus,
  // Prisma adapters (eliminates manual mapping!)
  prismaToAppointment,
  appointmentToPrismaCreate,
  appointmentToPrismaUpdate,
  buildAppointmentQuery,
  // Constants
  APPOINTMENT_STATUSES,
} from '@beauty-crm/platform-appointment-schema';

import { AppointmentDomain } from '../domain/models/appointment-refactored';
import type { PrismaClient } from '@prisma/client';

// ============================================================================
// 🎯 REPOSITORY INTERFACE
// ============================================================================

export interface IAppointmentRepository {
  findById(id: string): Promise<Appointment | null>;
  findBySalonId(
    salonId: string,
    filters?: AppointmentFilters,
  ): Promise<Appointment[]>;
  findByCustomerId(customerId: string): Promise<Appointment[]>;
  findByDateRange(
    salonId: string,
    startDate: Date,
    endDate: Date,
  ): Promise<Appointment[]>;
  findOverlapping(appointment: Appointment): Promise<Appointment[]>;
  create(request: CreateAppointmentRequest): Promise<Appointment>;
  update(appointment: Appointment): Promise<Appointment>;
  delete(id: string): Promise<void>;
  findAvailableSlots(
    salonId: string,
    date: Date,
    duration: number,
  ): Promise<TimeSlot[]>;
}

export interface AppointmentFilters {
  status?: AppointmentStatus;
  staffId?: string;
  treatmentId?: string;
  startDate?: Date;
  endDate?: Date;
}

export interface TimeSlot {
  start: Date;
  end: Date;
  available: boolean;
}

// ============================================================================
// 🔥 REPOSITORY IMPLEMENTATION - Zero Manual Mapping!
// ============================================================================

export class AppointmentRepository implements IAppointmentRepository {
  constructor(private prisma: PrismaClient) {}

  async findById(id: string): Promise<Appointment | null> {
    const prismaAppointment = await this.prisma.appointment.findUnique({
      where: { id },
    });

    // ✨ Auto-conversion using schema library adapter
    return prismaAppointment ? prismaToAppointment(prismaAppointment) : null;
  }

  async findBySalonId(
    salonId: string,
    filters: AppointmentFilters = {},
  ): Promise<Appointment[]> {
    // ✨ Type-safe query builder from schema library
    const where = buildAppointmentQuery({
      salonId,
      status: filters.status,
      staffId: filters.staffId,
      startDate: filters.startDate,
      endDate: filters.endDate,
    });

    const prismaAppointments = await this.prisma.appointment.findMany({
      where,
      orderBy: { startTime: 'asc' },
    });

    // ✨ Batch conversion using schema library
    return prismaAppointments.map(prismaToAppointment);
  }

  async findByCustomerId(customerId: string): Promise<Appointment[]> {
    const prismaAppointments = await this.prisma.appointment.findMany({
      where: { customerId },
      orderBy: { startTime: 'desc' },
    });

    return prismaAppointments.map(prismaToAppointment);
  }

  async findByDateRange(
    salonId: string,
    startDate: Date,
    endDate: Date,
  ): Promise<Appointment[]> {
    const where = buildAppointmentQuery({
      salonId,
      startDate,
      endDate,
    });

    const prismaAppointments = await this.prisma.appointment.findMany({
      where,
      orderBy: { startTime: 'asc' },
    });

    return prismaAppointments.map(prismaToAppointment);
  }

  async findOverlapping(appointment: Appointment): Promise<Appointment[]> {
    const prismaAppointments = await this.prisma.appointment.findMany({
      where: {
        salonId: appointment.salonId,
        staffId: appointment.staffId,
        id: { not: appointment.id },
        OR: [
          {
            startTime: {
              lt: appointment.endTime,
            },
            endTime: {
              gt: appointment.startTime,
            },
          },
        ],
        status: {
          notIn: [
            APPOINTMENT_STATUSES.CANCELLED,
            APPOINTMENT_STATUSES.COMPLETED,
          ],
        },
      },
    });

    return prismaAppointments.map(prismaToAppointment);
  }

  async create(request: CreateAppointmentRequest): Promise<Appointment> {
    // ✨ Auto-conversion using schema library adapter
    const prismaData = appointmentToPrismaCreate(request);

    const created = await this.prisma.appointment.create({
      data: prismaData,
    });

    return prismaToAppointment(created);
  }

  async update(appointment: Appointment): Promise<Appointment> {
    // ✨ Auto-conversion using schema library adapter
    const updateData = appointmentToPrismaUpdate(appointment);

    const updated = await this.prisma.appointment.update({
      where: { id: appointment.id },
      data: updateData,
    });

    return prismaToAppointment(updated);
  }

  async delete(id: string): Promise<void> {
    await this.prisma.appointment.delete({
      where: { id },
    });
  }

  async findAvailableSlots(
    salonId: string,
    date: Date,
    duration: number,
  ): Promise<TimeSlot[]> {
    const startOfDay = new Date(date);
    startOfDay.setHours(0, 0, 0, 0);

    const endOfDay = new Date(date);
    endOfDay.setHours(23, 59, 59, 999);

    // Get all appointments for the day
    const appointments = await this.findByDateRange(
      salonId,
      startOfDay,
      endOfDay,
    );

    // Generate time slots (9 AM to 6 PM, 30-minute intervals)
    const slots: TimeSlot[] = [];
    const startHour = 9;
    const endHour = 18;
    const slotInterval = 30; // minutes

    for (let hour = startHour; hour < endHour; hour++) {
      for (let minute = 0; minute < 60; minute += slotInterval) {
        const slotStart = new Date(date);
        slotStart.setHours(hour, minute, 0, 0);

        const slotEnd = new Date(slotStart);
        slotEnd.setMinutes(slotEnd.getMinutes() + duration);

        // Check if slot conflicts with existing appointments
        const isAvailable = !appointments.some((appointment) => {
          return (
            slotStart < appointment.endTime &&
            slotEnd > appointment.startTime &&
            appointment.status !== APPOINTMENT_STATUSES.CANCELLED
          );
        });

        slots.push({
          start: slotStart,
          end: slotEnd,
          available: isAvailable,
        });
      }
    }

    return slots;
  }

  // ============================================================================
  // 🎯 PLANNER-SPECIFIC QUERIES
  // ============================================================================

  async findPendingAppointments(salonId: string): Promise<Appointment[]> {
    return this.findBySalonId(salonId, {
      status: APPOINTMENT_STATUSES.PENDING,
    });
  }

  async findTodaysAppointments(salonId: string): Promise<Appointment[]> {
    const today = new Date();
    const startOfDay = new Date(today);
    startOfDay.setHours(0, 0, 0, 0);

    const endOfDay = new Date(today);
    endOfDay.setHours(23, 59, 59, 999);

    return this.findByDateRange(salonId, startOfDay, endOfDay);
  }

  async findUpcomingAppointments(
    salonId: string,
    days: number = 7,
  ): Promise<Appointment[]> {
    const now = new Date();
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + days);

    return this.findByDateRange(salonId, now, futureDate);
  }

  async findAppointmentsByTreatment(
    salonId: string,
    treatmentId: string,
  ): Promise<Appointment[]> {
    const prismaAppointments = await this.prisma.appointment.findMany({
      where: {
        salonId,
        treatmentId,
      },
      orderBy: { startTime: 'desc' },
    });

    return prismaAppointments.map(prismaToAppointment);
  }

  async findAppointmentsByStaff(
    salonId: string,
    staffId: string,
    date?: Date,
  ): Promise<Appointment[]> {
    const filters: AppointmentFilters = { staffId };

    if (date) {
      const startOfDay = new Date(date);
      startOfDay.setHours(0, 0, 0, 0);

      const endOfDay = new Date(date);
      endOfDay.setHours(23, 59, 59, 999);

      filters.startDate = startOfDay;
      filters.endDate = endOfDay;
    }

    return this.findBySalonId(salonId, filters);
  }

  // ============================================================================
  // 🔄 DOMAIN INTEGRATION HELPERS
  // ============================================================================

  async findDomainById(id: string): Promise<AppointmentDomain | null> {
    const appointment = await this.findById(id);
    return appointment ? AppointmentDomain.fromAppointment(appointment) : null;
  }

  async createFromDomain(
    domain: AppointmentDomain,
  ): Promise<AppointmentDomain> {
    const appointment = await this.update(domain.toAppointment());
    return AppointmentDomain.fromAppointment(appointment);
  }

  async updateFromDomain(
    domain: AppointmentDomain,
  ): Promise<AppointmentDomain> {
    const appointment = await this.update(domain.toAppointment());
    return AppointmentDomain.fromAppointment(appointment);
  }

  // ============================================================================
  // 📊 ANALYTICS QUERIES
  // ============================================================================

  async getAppointmentStats(
    salonId: string,
    startDate: Date,
    endDate: Date,
  ): Promise<{
    total: number;
    confirmed: number;
    completed: number;
    cancelled: number;
    revenue: number;
  }> {
    const appointments = await this.findByDateRange(
      salonId,
      startDate,
      endDate,
    );

    return {
      total: appointments.length,
      confirmed: appointments.filter(
        (a) => a.status === APPOINTMENT_STATUSES.CONFIRMED,
      ).length,
      completed: appointments.filter(
        (a) => a.status === APPOINTMENT_STATUSES.COMPLETED,
      ).length,
      cancelled: appointments.filter(
        (a) => a.status === APPOINTMENT_STATUSES.CANCELLED,
      ).length,
      revenue: appointments
        .filter((a) => a.status === APPOINTMENT_STATUSES.COMPLETED)
        .reduce((sum, a) => sum + a.treatmentPrice, 0),
    };
  }
}
