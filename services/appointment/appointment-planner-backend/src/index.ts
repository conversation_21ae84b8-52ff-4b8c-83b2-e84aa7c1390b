// import { Hono } from 'hono';
// import { logger } from 'hono/logger';
// import appointmentRoutes from './routes/appointmentRoutes';

// const app = new Hono();

// app.use('*', logger());

// app.route('/appointments', appointmentRoutes);

// app.get('/', (c) => {
//   return c.text('Appointment Planner Backend is running!');
// });

// export default {
//   port: process.env.PORT || 5016,
//   fetch: app.fetch,
// };
import { createComputingInstance } from '@beauty-crm/platform-computing-lifecycle';
import { AppointmentRepository } from '@infrastructure/repositories/appointmentRepository';
import { PrismaClient } from '@prisma/client';
import { type Context, Hono } from 'hono';
import type { HonoEnv } from 'hono/dist/types/types';
import { AppointmentService } from './application/services/appointmentService';
import { OutboxRelayer } from './infrastructure/events/OutboxRelayer';
import { EmailService } from './infrastructure/services/emailService';
import { AppointmentController } from './presentation/controllers/appointmentController';

// Initialize Prisma client
const prisma = new PrismaClient({
  log: ['query', 'info', 'warn', 'error'],
});

// Setup repositories and services
const appointmentRepository = new AppointmentRepository(prisma);
const emailService = new EmailService();
const outboxRelayer = new OutboxRelayer(prisma);
const appointmentService = new AppointmentService(
  appointmentRepository,
  emailService,
  outboxRelayer,
);

// Setup controllers
const appointmentController = new AppointmentController(
  appointmentService
  // {} as Context<HonoEnv>,
  // {} as Context<HonoEnv>,
);

// Setup routes
const app = new Hono();

app.get(
  '/api/v1/appointments/available-slots',
  appointmentController.getAvailableTimeSlots,
);

app.post('/api/v1/appointments', appointmentController.createAppointment);
// app.post('/appointments', appointmentController.create);

// Create application handler for platform computing lifecycle
class AppointmentPlannerHandler {
  async handle(request: Request, _context: unknown): Promise<Response> {
    return app.fetch(request);
  }
}

// Start server using platform computing lifecycle
async function startServer() {
  try {
    const port = Number.parseInt(process.env.PORT || '4000');

    console.log('🚀 Starting Appointment Planner Backend (Simplified)...');
    console.log(`📍 Port: ${port}`);
    console.log(
      `🔗 API: http://localhost:${port}/api/v1/appointments/available-slots`,
    );

    // Create computing instance with platform lifecycle
    const instance = createComputingInstance({
      bindings: {
        outboxRelayer: outboxRelayer,
        variables: process.env as Record<string, string>,
      },
      handler: new AppointmentPlannerHandler(),
      onStart: async () => {
        const natsUrl = process.env.NATS_URL || 'nats://localhost:4222';
        await outboxRelayer.start(natsUrl);
      },
      onStop: async () => {
        await outboxRelayer.stop();
      },
      platform: 'bun',
      port,
    });

    // Start the server
    await instance.start();
    console.log(`✅ Appointment Planner Backend running on port ${port}`);
  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
}

startServer();

// Export the app for Vite
export { app };
