import { zValida<PERSON> } from '@hono/zod-validator';
import type { Context } from 'hono';
import type { Env as HonoEnv } from 'hono/types';
import { z } from 'zod';
import type { AppointmentAggregateService } from '../../application/services/AppointmentAggregateService';
import type { AppointmentService } from '../../application/services/appointmentService';
import { appointmentEventPublisher } from '../../infrastructure/events/AppointmentEventPublisher';
import type { AppointmentSyncService } from '../../infrastructure/services/appointmentSyncService';

// ✨ Using Single Source of Truth schema library
import {
  validateCreateRequest,
  CreateAppointmentSchema,
  APPOINTMENT_STATUSES,
} from '@beauty-crm/platform-appointment-schema';

// ✨ Use schema library validation instead of duplicate schemas
const createAppointmentSchema = CreateAppointmentSchema.extend({
  startTime: z.string().transform((val) => new Date(val)),
  endTime: z.string().transform((val) => new Date(val)),
});

const availableSlotsSchema = z.object({
  salonId: z.string(),
});

// Define types
type AppointmentInput = z.infer<typeof createAppointmentSchema>;

type ValidatedContext<T> = Context<HonoEnv> & {
  req: {
    valid: (target: string) => T;
  };
};

export class AppointmentController {
  // private eventBus: RedisEventBus;

  constructor(
    private appointmentService: AppointmentService,
    private syncService: AppointmentSyncService,
    private appointmentAggregateService?: AppointmentAggregateService
  ) {
    // TODO: Initialize Redis event bus when infrastructure is ready
    // this.eventBus = new RedisEventBus({
    //   host: process.env.REDIS_HOST || 'localhost',
    //   port: Number.parseInt(process.env.REDIS_PORT || '6379'),
    // });
    // this.eventBus
    //   .connect()
    //   .catch((error) =>
    //     console.error('Failed to connect to Redis Event Bus:', error),
    //   );
  }

  // Event publishing for cross-system integration
  private async publishAppointmentEvent(
    eventType: string,
    appointment: unknown
  ): Promise<void> {
    try {
      // TODO: Integrate with Redis event bus when infrastructure is ready
      console.log(`Publishing ${eventType} event:`, appointment);

      // For now, log the event - will be replaced with actual Redis publishing
      const event = {
        appointment,
        eventId: crypto.randomUUID(),
        eventType,
        source: 'planner',
        timestamp: new Date().toISOString(),
      };

      // This will become: await this.eventBus.publishAppointmentEvent(event);
      console.log('Event published:', event);
    } catch (error) {
      console.error('Failed to publish appointment event:', error);
    }
  }

  // Get all appointments for a salon
  getAppointmentsBySalon = async (c: Context) => {
    const salonId = c.req.param('salonId');
    const appointments = await this.appointmentService.getAppointmentsByDate(
      salonId,
      new Date()
    );
    return c.json(appointments);
  };

  // Get an appointment by ID
  getAppointmentById = async (c: Context) => {
    const id = c.req.param('id');
    const appointments = await this.appointmentService.getAppointmentsByDate(
      id,
      new Date()
    );
    const appointment = appointments[0];

    if (!appointment) {
      return c.json({ error: 'Appointment not found' }, 404);
    }

    return c.json(appointment);
  };

  createAppointmentValidator = zValidator('json', createAppointmentSchema);
  getAvailableTimeSlotsValidator = zValidator('query', availableSlotsSchema);

  // New event-driven appointment creation using aggregate
  createAppointmentV2 = async (c: ValidatedContext<AppointmentInput>) => {
    try {
      if (!this.appointmentAggregateService) {
        // Fallback to original method if aggregate service not available
        return this.createAppointment(c);
      }

      const formData = c.req.valid('json') as Record<string, unknown>;

      // Transform and validate data
      const request = {
        correlationId: c.req.header('x-correlation-id') || crypto.randomUUID(),
        customerEmail: String(formData.customerEmail || ''),
        customerName: String(formData.customerName || ''),
        customerPhone: formData.customerPhone
          ? String(formData.customerPhone)
          : undefined,
        endTime: new Date(String(formData.endTime)),
        locale: formData.locale ? String(formData.locale) : 'en',
        notes: formData.notes ? String(formData.notes) : undefined,
        salonColor: formData.salonColor
          ? String(formData.salonColor)
          : undefined,
        salonId: String(formData.salonId || ''),
        salonLogo: formData.salonLogo ? String(formData.salonLogo) : undefined,
        salonName: String(formData.salonName || ''),
        staffId: formData.staffId ? String(formData.staffId) : undefined,
        startTime: new Date(String(formData.startTime)),
        treatmentDuration: Number(formData.treatmentDuration || 60),
        treatmentName: String(formData.treatmentName || ''),
        treatmentPrice: Number(formData.treatmentPrice || 0),
        // Add request metadata
        userId: c.req.header('x-user-id'),
      };

      // Use aggregate service for event-driven creation
      const result = await this.appointmentAggregateService.createAppointment(
        request
      );

      if (!result.success) {
        return c.json(
          {
            appointmentId: result.appointmentId,
            error: result.error || 'Failed to create appointment',
          },
          400
        );
      }

      // Return the created appointment
      return c.json(
        {
          appointment: result.appointment,
          appointmentId: result.appointmentId,
          success: true,
          version: result.version,
        },
        201
      );
    } catch (error) {
      console.error('Error in createAppointmentV2:', error);
      return c.json(
        {
          error:
            error instanceof Error
              ? error.message
              : 'Failed to create appointment',
        },
        500
      );
    }
  };

  createAppointment = async (c: ValidatedContext<AppointmentInput>) => {
    try {
      const formData = c.req.valid('json') as Record<string, unknown>;
      // Safe type assertion for appointment service
      const data = {
        ...formData,
        endTime: formData.endTime
          ? new Date(String(formData.endTime))
          : undefined,
        startTime: formData.startTime
          ? new Date(String(formData.startTime))
          : undefined,
        treatmentDuration: formData.treatmentDuration
          ? Number(formData.treatmentDuration)
          : undefined,
        treatmentPrice: formData.treatmentPrice
          ? Number(formData.treatmentPrice)
          : undefined,
      };

      const appointment = await this.appointmentService.createAppointment(data);

      // Sync appointment to management schema
      await this.syncService.syncToManagementSchema(appointment);

      // Publish appointment created event for cross-system sync
      await appointmentEventPublisher.publishAppointmentCreated(appointment);

      return c.json(appointment, 201);
    } catch (error) {
      if (
        error instanceof Error &&
        (error.message.includes('already booked') ||
          error.message.includes('Time slot is not available'))
      ) {
        // Enhanced error response with next steps
        const formData = c.req.valid('json') as Record<string, unknown>;
        const salonId = String(formData.salonId || '');
        const startTime = formData.startTime
          ? new Date(String(formData.startTime))
          : new Date();
        const treatmentDuration = formData.treatmentDuration
          ? Number(formData.treatmentDuration)
          : 30;

        return c.json(
          {
            error: error.message,
            nextAvailableSlots: await this.getSuggestedSlots(
              salonId,
              startTime,
              treatmentDuration
            ),
            suggestions: [
              'Try selecting a different time',
              'Check the calendar for other available slots',
              'Consider appointment on another day',
            ],
          },
          409
        );
      }
      return c.json(
        {
          error:
            error instanceof Error
              ? error.message
              : 'Failed to create appointment',
        },
        400
      );
    }
  };

  // Confirm an appointment
  confirmAppointment = async (c: Context) => {
    try {
      const id = c.req.param('id');
      const appointments = await this.appointmentService.getAppointmentsByDate(
        id,
        new Date()
      );
      const appointment = appointments[0];
      if (!appointment) {
        return c.json({ error: 'Appointment not found' }, 404);
      }
      return c.json(appointment);
    } catch (error) {
      return c.json(
        {
          error:
            error instanceof Error
              ? error.message
              : 'Failed to confirm appointment',
        },
        400
      );
    }
  };

  // Cancel an appointment
  cancelAppointment = async (c: Context) => {
    try {
      const id = c.req.param('id');
      const appointment = await this.appointmentService.cancelAppointment(id);
      return c.json(appointment);
    } catch (error) {
      return c.json(
        {
          error:
            error instanceof Error
              ? error.message
              : 'Failed to cancel appointment',
        },
        400
      );
    }
  };

  // Complete an appointment
  completeAppointment = async (c: Context) => {
    try {
      const id = c.req.param('id');
      const appointments = await this.appointmentService.getAppointmentsByDate(
        id,
        new Date()
      );
      const appointment = appointments[0];
      if (!appointment) {
        return c.json({ error: 'Appointment not found' }, 404);
      }
      return c.json(appointment);
    } catch (error) {
      return c.json(
        {
          error:
            error instanceof Error
              ? error.message
              : 'Failed to complete appointment',
        },
        400
      );
    }
  };
}
