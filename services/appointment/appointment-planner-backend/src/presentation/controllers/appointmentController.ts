import type { Context } from 'hono';
import { z } from 'zod';
import type { AppointmentService } from '../../application/services/appointmentService';
import { CreateAppointmentSchema } from '@beauty-crm/platform-appointment-schema';

const CUID_REGEX = /^c[a-z0-9]{24}$/;

const appointmentSchema = CreateAppointmentSchema.extend({
  customerId: z
    .string()
    .regex(CUID_REGEX, 'Customer ID must be a valid CUID format'),
  staffId: z.string().regex(CUID_REGEX, 'Staff ID must be a valid CUID format'),
  treatmentId: z
    .string()
    .regex(CUID_REGEX, 'Treatment ID must be a valid CUID format'),
});

export class AppointmentController {
  constructor(private appointmentService: AppointmentService) {}

  createAppointment = async (c: Context) => {
    try {
      const body = await c.req.json();

      const validationResult = appointmentSchema.safeParse(body);
      if (!validationResult.success) {
        return c.json(
          {
            details: validationResult.error.errors,
            error: 'Invalid appointment data',
            success: false,
          },
          400,
        );
      }

      const appointment = await this.appointmentService.createAppointment(
        validationResult.data,
      );

      return c.json({ data: appointment, success: true }, 201);
    } catch (error) {
      console.error('Error creating appointment:', error);
      return c.json(
        {
          error: error instanceof Error ? error.message : 'Unknown error',
          success: false,
        },
        500,
      );
    }
  };

  getAvailableTimeSlots = async (c: Context) => {
    // TODO: Implement
    return c.json({ slots: [] });
  };
}
