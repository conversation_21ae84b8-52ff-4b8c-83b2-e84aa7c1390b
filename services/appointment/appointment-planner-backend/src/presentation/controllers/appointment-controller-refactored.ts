/**
 * 🔥 REFACTORED APPOINTMENT CONTROLLER - Using Single Source of Truth
 *
 * This eliminates ALL duplicate Zod schemas and uses the schema library validation.
 * Clean, consistent, and maintainable!
 */

import type { Context } from 'hono';
import { zValidator } from '@hono/zod-validator';
import { z } from 'zod';

import {
  // Core types (no more duplicate interfaces!)
  Appointment,
  CreateAppointmentRequest,
  AppointmentStatus,
  // Validation (no more duplicate Zod schemas!)
  validateCreateRequest,
  validateUpdateRequest,
  CreateAppointmentSchema,
  UpdateAppointmentSchema,
  // Constants (no more magic strings!)
  APPOINTMENT_STATUSES,
  APPOINTMENT_EVENT_TYPES,
} from '@beauty-crm/platform-appointment-schema';

import { AppointmentService } from '../services/appointment-service-refactored';

// ============================================================================
// 🎯 CONTROLLER CLASS - Clean and Focused
// ============================================================================

export class AppointmentController {
  constructor(private appointmentService: AppointmentService) {}

  // ============================================================================
  // 📝 VALIDATION MIDDLEWARE - Using Schema Library
  // ============================================================================

  // ✨ Single validation schema for all services
  createAppointmentValidator = zValidator('json', CreateAppointmentSchema);
  updateAppointmentValidator = zValidator('json', UpdateAppointmentSchema);

  // Query parameter validators
  salonIdValidator = zValidator(
    'param',
    z.object({
      salonId: z.string().cuid(),
    }),
  );

  appointmentIdValidator = zValidator(
    'param',
    z.object({
      id: z.string().cuid(),
    }),
  );

  availableSlotsValidator = zValidator(
    'query',
    z.object({
      salonId: z.string().cuid(),
      date: z.string().transform((val) => new Date(val)),
      duration: z
        .string()
        .transform((val) => parseInt(val))
        .optional()
        .default('60'),
    }),
  );

  // ============================================================================
  // 🎯 APPOINTMENT CRUD OPERATIONS
  // ============================================================================

  createAppointment = async (c: Context) => {
    try {
      // ✨ Validation handled by middleware using schema library
      const requestData = c.req.valid('json') as CreateAppointmentRequest;

      // Additional validation using schema library
      const validatedData = validateCreateRequest(requestData);

      const appointment =
        await this.appointmentService.createAppointment(validatedData);

      return c.json(
        {
          success: true,
          data: appointment,
          message: 'Appointment created successfully',
        },
        201,
      );
    } catch (error) {
      return c.json(
        {
          success: false,
          error:
            error instanceof Error
              ? error.message
              : 'Failed to create appointment',
        },
        400,
      );
    }
  };

  getAppointmentById = async (c: Context) => {
    try {
      const { id } = c.req.valid('param');

      const appointment = await this.appointmentService.getAppointmentById(id);

      if (!appointment) {
        return c.json(
          {
            success: false,
            error: 'Appointment not found',
          },
          404,
        );
      }

      return c.json({
        success: true,
        data: appointment,
      });
    } catch (error) {
      return c.json(
        {
          success: false,
          error:
            error instanceof Error
              ? error.message
              : 'Failed to get appointment',
        },
        500,
      );
    }
  };

  getAppointmentsBySalon = async (c: Context) => {
    try {
      const { salonId } = c.req.valid('param');
      const query = c.req.query();

      // Parse query parameters
      const filters = {
        status: query.status as AppointmentStatus | undefined,
        staffId: query.staffId,
        treatmentId: query.treatmentId,
        startDate: query.startDate ? new Date(query.startDate) : undefined,
        endDate: query.endDate ? new Date(query.endDate) : undefined,
      };

      const appointments = await this.appointmentService.getAppointmentsBySalon(
        salonId,
        filters,
      );

      return c.json({
        success: true,
        data: appointments,
        count: appointments.length,
      });
    } catch (error) {
      return c.json(
        {
          success: false,
          error:
            error instanceof Error
              ? error.message
              : 'Failed to get appointments',
        },
        500,
      );
    }
  };

  updateAppointment = async (c: Context) => {
    try {
      const { id } = c.req.valid('param');
      const updateData = c.req.valid('json');

      // ✨ Validation using schema library
      const validatedData = validateUpdateRequest({ id, ...updateData });

      const appointment =
        await this.appointmentService.updateAppointment(validatedData);

      return c.json({
        success: true,
        data: appointment,
        message: 'Appointment updated successfully',
      });
    } catch (error) {
      return c.json(
        {
          success: false,
          error:
            error instanceof Error
              ? error.message
              : 'Failed to update appointment',
        },
        400,
      );
    }
  };

  deleteAppointment = async (c: Context) => {
    try {
      const { id } = c.req.valid('param');

      await this.appointmentService.deleteAppointment(id);

      return c.json({
        success: true,
        message: 'Appointment deleted successfully',
      });
    } catch (error) {
      return c.json(
        {
          success: false,
          error:
            error instanceof Error
              ? error.message
              : 'Failed to delete appointment',
        },
        500,
      );
    }
  };

  // ============================================================================
  // 🎯 APPOINTMENT STATUS OPERATIONS
  // ============================================================================

  confirmAppointment = async (c: Context) => {
    try {
      const { id } = c.req.valid('param');

      const appointment = await this.appointmentService.confirmAppointment(id);

      return c.json({
        success: true,
        data: appointment,
        message: 'Appointment confirmed successfully',
      });
    } catch (error) {
      return c.json(
        {
          success: false,
          error:
            error instanceof Error
              ? error.message
              : 'Failed to confirm appointment',
        },
        400,
      );
    }
  };

  cancelAppointment = async (c: Context) => {
    try {
      const { id } = c.req.valid('param');
      const { reason } = await c.req.json();

      const appointment = await this.appointmentService.cancelAppointment(
        id,
        reason,
      );

      return c.json({
        success: true,
        data: appointment,
        message: 'Appointment cancelled successfully',
      });
    } catch (error) {
      return c.json(
        {
          success: false,
          error:
            error instanceof Error
              ? error.message
              : 'Failed to cancel appointment',
        },
        400,
      );
    }
  };

  completeAppointment = async (c: Context) => {
    try {
      const { id } = c.req.valid('param');

      const appointment = await this.appointmentService.completeAppointment(id);

      return c.json({
        success: true,
        data: appointment,
        message: 'Appointment completed successfully',
      });
    } catch (error) {
      return c.json(
        {
          success: false,
          error:
            error instanceof Error
              ? error.message
              : 'Failed to complete appointment',
        },
        400,
      );
    }
  };

  rescheduleAppointment = async (c: Context) => {
    try {
      const { id } = c.req.valid('param');
      const { startTime, endTime } = await c.req.json();

      const appointment = await this.appointmentService.rescheduleAppointment(
        id,
        new Date(startTime),
        new Date(endTime),
      );

      return c.json({
        success: true,
        data: appointment,
        message: 'Appointment rescheduled successfully',
      });
    } catch (error) {
      return c.json(
        {
          success: false,
          error:
            error instanceof Error
              ? error.message
              : 'Failed to reschedule appointment',
        },
        400,
      );
    }
  };

  // ============================================================================
  // 🎯 AVAILABILITY & SCHEDULING
  // ============================================================================

  getAvailableSlots = async (c: Context) => {
    try {
      const { salonId, date, duration } = c.req.valid('query');

      const slots = await this.appointmentService.getAvailableSlots(
        salonId,
        date,
        duration,
      );

      return c.json({
        success: true,
        data: slots,
        count: slots.filter((slot) => slot.available).length,
      });
    } catch (error) {
      return c.json(
        {
          success: false,
          error:
            error instanceof Error
              ? error.message
              : 'Failed to get available slots',
        },
        500,
      );
    }
  };

  getTodaysAppointments = async (c: Context) => {
    try {
      const { salonId } = c.req.valid('param');

      const appointments =
        await this.appointmentService.getTodaysAppointments(salonId);

      return c.json({
        success: true,
        data: appointments,
        count: appointments.length,
      });
    } catch (error) {
      return c.json(
        {
          success: false,
          error:
            error instanceof Error
              ? error.message
              : "Failed to get today's appointments",
        },
        500,
      );
    }
  };

  getUpcomingAppointments = async (c: Context) => {
    try {
      const { salonId } = c.req.valid('param');
      const days = parseInt(c.req.query('days') || '7');

      const appointments =
        await this.appointmentService.getUpcomingAppointments(salonId, days);

      return c.json({
        success: true,
        data: appointments,
        count: appointments.length,
      });
    } catch (error) {
      return c.json(
        {
          success: false,
          error:
            error instanceof Error
              ? error.message
              : 'Failed to get upcoming appointments',
        },
        500,
      );
    }
  };

  // ============================================================================
  // 📊 ANALYTICS & REPORTING
  // ============================================================================

  getAppointmentStats = async (c: Context) => {
    try {
      const { salonId } = c.req.valid('param');
      const startDate = new Date(
        c.req.query('startDate') || new Date().toISOString(),
      );
      const endDate = new Date(
        c.req.query('endDate') || new Date().toISOString(),
      );

      const stats = await this.appointmentService.getAppointmentStats(
        salonId,
        startDate,
        endDate,
      );

      return c.json({
        success: true,
        data: stats,
      });
    } catch (error) {
      return c.json(
        {
          success: false,
          error:
            error instanceof Error
              ? error.message
              : 'Failed to get appointment stats',
        },
        500,
      );
    }
  };
}
