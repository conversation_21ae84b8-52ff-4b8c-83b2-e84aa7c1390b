generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Appointment {
  id                   String            @id @default(cuid())
  salonId              String
  customerId           String
  staffId              String?
  treatmentId          String

  // Customer info (denormalized for performance)
  customerName         String
  customerEmail        String
  customerPhone        String?

  // Treatment info (denormalized for performance)
  treatmentName        String
  treatmentDuration    Int
  treatmentPrice       Float

  // Salon info (denormalized for performance)
  salonName            String
  salonLogo            String?
  salonColor           String?

  // Scheduling
  startTime            DateTime
  endTime              DateTime

  // Status & metadata
  status               AppointmentStatus @default(PENDING)
  notes                String?
  locale               String            @default("en-US")

  // Source tracking
  source               String            @default("planner")
  plannerAppointmentId String?

  // Timestamps
  createdAt            DateTime          @default(now())
  updatedAt            DateTime          @updatedAt
  confirmedAt          DateTime?
  completedAt          DateTime?
  cancelledAt          DateTime?

  @@map("appointments")
  @@index([salonId, startTime])
  @@index([customerId])
  @@index([status])
  @@index([staffId])
  @@index([treatmentId])
}

enum AppointmentStatus {
  PENDING
  CONFIRMED
  SCHEDULED
  IN_PROGRESS
  COMPLETED
  CANCELLED
  NO_SHOW
  RESCHEDULED
}

// Treatment model removed - now fetched directly from treatment service API

// Sprint 6: Netherlands Market - Service Categories with Dutch Terminology
model TreatmentCategory {
  id            String   @id @default(uuid())
  categoryKey   String   @unique
  dutchName     String
  englishName   String
  descriptionNl String?
  descriptionEn String?
  createdAt     DateTime @default(now())

  @@map("treatment_categories_nl")
}

// Event-Driven Architecture - Transactional Outbox Pattern
model AppointmentOutbox {
  id            String    @id @default(uuid())
  aggregateId   String // ID of the appointment aggregate
  aggregateType String    @default("appointment")
  eventType     String // e.g., "appointment.events.created"
  eventId       String    @unique // Unique event identifier
  eventVersion  Int       @default(1)
  eventData     Json // Event payload as JSON
  eventMetadata Json? // Optional metadata (correlation, causation, etc.)
  source        String    @default("appointment-planner-backend")
  timestamp     DateTime  @default(now())
  processed     Boolean   @default(false)
  processedAt   DateTime?
  retryCount    Int       @default(0)
  lastRetryAt   DateTime?
  errorMessage  String?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  // Indexes for efficient querying and cleanup
  @@index([processed, createdAt]) // For finding unprocessed events
  @@index([aggregateId, aggregateType]) // For aggregate-specific queries
  @@index([eventType]) // For event type filtering
  @@index([createdAt]) // For cleanup operations (time-based)
  @@index([processedAt]) // For processed event cleanup
  @@map("appointment_outbox")
}

// Sprint 6: Netherlands Market - Dutch Holidays for Appointment Blocking
model NetherlandsHoliday {
  id          String   @id @default(uuid())
  date        DateTime
  name        String
  description String?
  year        Int
  createdAt   DateTime @default(now())

  @@unique([date])
  @@index([year])
  @@map("netherlands_holidays")
}
