{"compilerOptions": {"outDir": "./dist", "paths": {"@/*": ["./src/*"]}, "rootDir": "./src", "sourceMap": true, "tsBuildInfoFile": "./.tsbuildinfo"}, "exclude": ["node_modules", "dist", "__tests__/**/*", "src/tests/**/*", "src/mocks/**/*"], "extends": "../../../tsconfig.base.json", "include": ["src/**/*"], "references": [{"path": "../../../shared-platform-engineering/platform-db-client"}, {"path": "../../../shared-platform-engineering/platform-logger"}]}