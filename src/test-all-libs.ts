/**
 * 🎯 COMPLETE APPOINTMENT LIBRARIES TEST
 * 
 * This file demonstrates using ALL our appointment libraries together:
 * - @beauty-crm/platform-appointment-schema (validation & types)
 * - @beauty-crm/platform-appointment-domain (business logic)
 * - @beauty-crm/platform-appointment-eventing (events)
 */

// 🎯 Schema Library - Single Source of Truth
import {
  validateCreateRequest,
  createAppointmentCreatedEvent,
  APPOINTMENT_STATUSES,
  type CreateAppointmentRequest,
  type Appointment,
} from '@beauty-crm/platform-appointment-schema';

// 🎯 Domain Library - Business Logic
import {
  AppointmentDomain,
  AppointmentDomainService,
} from '@beauty-crm/platform-appointment-domain';

// 🎯 Eventing Library - Event Publishing
import {
  createAppointmentEventPublisher,
} from '@beauty-crm/platform-appointment-eventing';

// ============================================================================
// 🎯 SIMPLE APPOINTMENT FLOW TEST
// ============================================================================

export async function testAppointmentLibraries() {
  console.log('🚀 Starting Appointment Libraries Test...\n');

  try {
    // ============================================================================
    // 📝 STEP 1: CREATE APPOINTMENT REQUEST
    // ============================================================================
    
    console.log('📝 Step 1: Creating appointment request...');
    
    const appointmentRequest: CreateAppointmentRequest = {
      salonId: 'salon_123',
      customerId: 'customer_456',
      treatmentId: 'treatment_789',
      customerName: 'John Doe',
      customerEmail: '<EMAIL>',
      customerPhone: '+1234567890',
      treatmentName: 'Premium Haircut',
      treatmentDuration: 60,
      treatmentPrice: 75.00,
      salonName: 'Elite Hair Studio',
      salonLogo: 'https://example.com/logo.png',
      salonColor: '#FF6B6B',
      startTime: new Date('2025-01-15T10:00:00Z'),
      endTime: new Date('2025-01-15T11:00:00Z'),
      notes: 'Customer prefers shorter style',
      locale: 'en-US',
      source: 'planner-test',
    };

    console.log('✅ Appointment request created:', {
      customer: appointmentRequest.customerName,
      treatment: appointmentRequest.treatmentName,
      time: appointmentRequest.startTime.toISOString(),
    });

    // ============================================================================
    // 🔍 STEP 2: VALIDATE USING SCHEMA LIBRARY
    // ============================================================================
    
    console.log('\n🔍 Step 2: Validating request using schema library...');
    
    const validatedRequest = validateCreateRequest(appointmentRequest);
    console.log('✅ Schema validation passed');

    // ============================================================================
    // 🧠 STEP 3: PROCESS USING DOMAIN LIBRARY
    // ============================================================================
    
    console.log('\n🧠 Step 3: Processing business logic using domain library...');
    
    const domainService = new AppointmentDomainService();
    
    // Create appointment domain object
    const appointmentDomain = AppointmentDomain.fromCreateRequest(validatedRequest);
    console.log('✅ Domain object created:', {
      id: appointmentDomain.id,
      status: appointmentDomain.status,
      duration: appointmentDomain.getDurationInMinutes(),
    });
    
    // Test domain business rules
    console.log('✅ Domain validation:', {
      canBeModified: appointmentDomain.canBeModified(),
      isUpcoming: appointmentDomain.isUpcoming(),
      isPending: appointmentDomain.isPending(),
    });

    // ============================================================================
    // 📡 STEP 4: CREATE EVENT USING EVENTING LIBRARY
    // ============================================================================
    
    console.log('\n📡 Step 4: Creating event using eventing library...');
    
    const mockAppointment = appointmentDomain.toAppointment();
    const event = createAppointmentCreatedEvent(mockAppointment, {
      source: 'appointment-planner-test',
      userId: 'test-user',
      correlationId: `test-${Date.now()}`,
    });
    
    console.log('✅ Event created:', {
      eventType: event.eventType,
      appointmentId: event.data.appointmentId,
      timestamp: event.timestamp,
    });

    // ============================================================================
    // 🔄 STEP 5: TEST DOMAIN OPERATIONS
    // ============================================================================
    
    console.log('\n🔄 Step 5: Testing domain operations...');
    
    // Test confirmation
    appointmentDomain.confirm();
    console.log('✅ Appointment confirmed:', {
      status: appointmentDomain.status,
      isConfirmed: appointmentDomain.isConfirmed(),
    });
    
    // Test business rule validation
    const slots = domainService.generateAvailableSlots(
      new Date('2025-01-15'),
      60,
      [] // No existing appointments
    );
    console.log(`✅ Generated ${slots.length} time slots for availability checking`);

    // ============================================================================
    // 🎉 SUCCESS SUMMARY
    // ============================================================================
    
    console.log('\n🎉 APPOINTMENT LIBRARIES TEST SUCCESSFUL!\n');
    console.log('📋 Summary:');
    console.log('  ✅ Schema Library: Validation & type safety working');
    console.log('  ✅ Domain Library: Business logic & rules working');
    console.log('  ✅ Eventing Library: Event creation working');
    console.log('  ✅ All libraries integrated successfully!\n');
    
    return {
      success: true,
      appointmentId: appointmentDomain.id,
      message: 'All appointment libraries tested successfully',
    };

  } catch (error) {
    console.error('❌ Test failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

// ============================================================================
// 🚀 RUN TEST IF CALLED DIRECTLY
// ============================================================================

if (import.meta.main) {
  testAppointmentLibraries()
    .then((result) => {
      console.log('Final result:', result);
      process.exit(result.success ? 0 : 1);
    })
    .catch((error) => {
      console.error('Unhandled error:', error);
      process.exit(1);
    });
}
