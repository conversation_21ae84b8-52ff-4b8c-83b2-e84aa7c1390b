
graph TD
    A[Application Service] -- Writes Domain Event --> B[(Database)]
    A -- Writes Outbox Entry --> C[Outbox Table]
    C -- Unprocessed Events --> D[OutboxWorker]
    D -- Publishes Event --> E[NATS Broker]
    E -- Event Stream --> F[Downstream Service / Subscriber]

    subgraph Database
        B
        C
    end

    style A fill:#f9f,stroke:#333,stroke-width:2px
    style D fill:#bbf,stroke:#333,stroke-width:2px
    style E fill:#bfb,stroke:#333,stroke-width:2px
    style F fill:#ffd,stroke:#333,stroke-width:2px
```
